<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test the rDNS update functionality
$testIp = "**************"; // Replace with an actual IP
$testDomain = "test.example.com";

echo "Testing rDNS Update API\n";
echo "=======================\n";
echo "IP: $testIp\n";
echo "Domain: $testDomain\n\n";

// Test SolusVM API connection
$apiKey = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
$baseUrl = "https://virt.zetservers.com";

// Step 1: Test SolusVM API connection
echo "Step 1: Testing SolusVM API connection...\n";
$serversEndpoint = "$baseUrl/api/v1/servers";
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $serversEndpoint,
    CURLOPT_CUSTOMREQUEST => "GET",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        "Authorization: Bearer $apiKey",
        "Accept: application/json",
    ],
]);

$serversResponse = curl_exec($ch);
$serversCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Servers API response code: $serversCode\n";
if ($serversCode == 200) {
    $serversData = json_decode($serversResponse, true);
    echo "Found " . count($serversData["data"]) . " servers\n";
    
    // Look for any IP to test with
    foreach ($serversData["data"] as $server) {
        if (isset($server["ip_addresses"]["ipv4"]) && !empty($server["ip_addresses"]["ipv4"])) {
            $firstIp = $server["ip_addresses"]["ipv4"][0];
            $testIp = $firstIp["ip"];
            echo "Using test IP: $testIp\n";
            echo "IP ID: " . ($firstIp["id"] ?? "N/A") . "\n";
            break;
        }
    }
} else {
    echo "Error connecting to SolusVM API\n";
    echo "Response: " . substr($serversResponse, 0, 500) . "\n";
    exit;
}

echo "\nStep 2: Testing rDNS creation...\n";

// Find the IP ID and server ID for our test IP
$ipId = null;
$serverId = null;
foreach ($serversData["data"] as $server) {
    if (isset($server["ip_addresses"]["ipv4"]) && !empty($server["ip_addresses"]["ipv4"])) {
        foreach ($server["ip_addresses"]["ipv4"] as $ipData) {
            if ($ipData["ip"] === $testIp) {
                $ipId = $ipData["id"];
                $serverId = $server["id"];
                break 2;
            }
        }
    }
}

echo "Found IP ID: $ipId for IP: $testIp\n";
echo "Found Server ID: $serverId for IP: $testIp\n";

// Try server-specific rDNS creation first
echo "\nStep 2a: Testing server-specific rDNS creation...\n";
$serverRdnsEndpoint = "$baseUrl/api/v1/reverse_dns";
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $serverRdnsEndpoint,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POSTFIELDS => json_encode([
        "ip" => $testIp,
        "domain" => $testDomain,
        "ip_id" => $ipId
    ]),
    CURLOPT_HTTPHEADER => [
        "Authorization: Bearer $apiKey",
        "Accept: application/json",
        "Content-Type: application/json",
    ],
]);

$serverResponse = curl_exec($ch);
$serverCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Server-specific rDNS response code: $serverCode\n";
echo "Server-specific rDNS response: " . substr($serverResponse, 0, 500) . "\n";

if ($serverCode >= 200 && $serverCode < 300) {
    echo "Server-specific rDNS creation successful!\n";
} else {
    echo "\nStep 2b: Falling back to global rDNS creation...\n";
    
    // Test creating rDNS record globally
    $createEndpoint = "$baseUrl/api/v1/reverse_dns";
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $createEndpoint,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_RETURNTRANSFER => true,
                 CURLOPT_POSTFIELDS => json_encode([
             "ip" => $testIp,
             "ip_id" => $ipId,
             "domain" => $testDomain
         ]),
         CURLOPT_HTTPHEADER => [
             "Authorization: Bearer $apiKey",
             "Accept: application/json",
             "Content-Type: application/json",
         ],
     ]);

     $createResponse = curl_exec($ch);
     $createCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
     curl_close($ch);

     echo "Global rDNS response code: $createCode\n";
     echo "Global rDNS response: " . substr($createResponse, 0, 500) . "\n";
 }

 // Test updating if either method succeeded
 $successfulResponse = ($serverCode >= 200 && $serverCode < 300) ? $serverResponse : $createResponse;
 $successfulCode = ($serverCode >= 200 && $serverCode < 300) ? $serverCode : $createCode;

 if ($successfulCode >= 200 && $successfulCode < 300) {
     $responseData = json_decode($successfulResponse, true);
     if (isset($responseData["data"]["id"])) {
         $rdnsId = $responseData["data"]["id"];
         echo "\nrDNS record created with ID: $rdnsId\n";
         
         // Test updating the rDNS record
         echo "\nStep 3: Testing rDNS update...\n";
         $updateEndpoint = "$baseUrl/api/v1/reverse_dns/$rdnsId";
         $ch = curl_init();
         curl_setopt_array($ch, [
             CURLOPT_URL => $updateEndpoint,
             CURLOPT_CUSTOMREQUEST => "PATCH",
             CURLOPT_RETURNTRANSFER => true,
             CURLOPT_POSTFIELDS => json_encode([
                 "domain" => "updated-$testDomain"
             ]),
             CURLOPT_HTTPHEADER => [
                 "Authorization: Bearer $apiKey",
                 "Accept: application/json",
                 "Content-Type: application/json",
             ],
         ]);

         $updateResponse = curl_exec($ch);
         $updateCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
         curl_close($ch);

         echo "Update rDNS response code: $updateCode\n";
         echo "Update rDNS response: " . substr($updateResponse, 0, 500) . "\n";
     }
 }

echo "\nTest completed.\n";
?> 