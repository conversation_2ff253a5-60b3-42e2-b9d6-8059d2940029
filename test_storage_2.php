<?php
// Test specifically storage_id = 2 (2x500GB SSD)
require_once 'mysql.php';

echo "=== Testing Storage ID = 2 (2x500GB SSD) ===\n\n";

// Simulate the exact API call parameters
$model_id = 1;
$storage_id = 2; // This should trigger "1 week" delivery
$bandwidth_id = 1;
$location_id = 1;
$subnet_id = 1;
$os_id = 1;
$servers_number = 1;

echo "Parameters:\n";
echo "- model_id: $model_id\n";
echo "- storage_id: $storage_id (2x500GB SSD)\n";
echo "- bandwidth_id: $bandwidth_id\n";
echo "- location_id: $location_id\n";
echo "- subnet_id: $subnet_id\n";
echo "- os_id: $os_id\n";
echo "- servers_number: $servers_number\n\n";

// Get storage name
$storage_query = $pdo->prepare("SELECT name FROM dedicated_storages WHERE id = ?");
$storage_query->execute([$storage_id]);
$storage_result = $storage_query->fetch(PDO::FETCH_ASSOC);

if ($storage_result) {
    echo "Storage name: " . $storage_result['name'] . "\n\n";
    
    // Check the forced override logic from the API
    echo "=== Backend Logic Check ===\n";
    echo "The API has this forced override:\n";
    echo "if (\$storage_id == 2) {\n";
    echo "    // Force no exact match for 2x500GB SSD to test 1 week delivery\n";
    echo "    \$exact_match_count = 0;\n";
    echo "}\n\n";
    
    echo "Since storage_id = $storage_id, the exact_match_count will be forced to 0.\n";
    echo "This should trigger the 'No exact match' logic:\n";
    echo "- has_available_servers = false\n";
    echo "- best_delivery_time = '1 week'\n";
    echo "- stock_color = 'orange'\n";
    echo "- stock_message = 'Configuration requires setup'\n\n";
    
    echo "Expected API response should contain:\n";
    echo "\"delivery\": \"1 week\"\n\n";
    
    echo "=== Test Instructions ===\n";
    echo "1. In the browser, select storage option '2x500GB SSD'\n";
    echo "2. Check the console logs for:\n";
    echo "   - 'Fetching dedicatedorder with params: {storage_id: 2, ...}'\n";
    echo "   - API response should contain '\"delivery\":\"1 week\"'\n";
    echo "   - Frontend should log '=== processConfigData called ==='\n";
    echo "   - Frontend should log 'Delivery time from API: 1 week'\n";
    echo "   - Frontend should log 'Setting delivery time to: 1 week'\n\n";
    
    echo "If you're not seeing these logs, the issue might be:\n";
    echo "1. Frontend code not being executed (cache issue?)\n";
    echo "2. Different storage_id being sent\n";
    echo "3. API response structure different than expected\n";
}

echo "\n=== Debug Complete ===\n";
?>
