<?php
/**
 * Reset PayPal payment methods for clean testing
 */

require_once("./mysql.php");

try {
    echo "Resetting PayPal payment methods for testing...\n\n";
    
    // Show current PayPal payment methods
    $stmt = $pdo->prepare("SELECT * FROM payment_methods WHERE user_id = 1 AND processor = 'paypal'");
    $stmt->execute();
    $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Current PayPal payment methods:\n";
    foreach ($methods as $method) {
        echo "- ID: {$method['id']}, Status: {$method['status']}, Setup Token: {$method['setup_token']}, Vault ID: " . ($method['vault_id'] ?: 'None') . "\n";
    }
    echo "\n";
    
    // Ask for confirmation
    echo "This will delete all PayPal payment methods for user 1.\n";
    echo "You can then test the complete flow through the frontend.\n";
    echo "Type 'yes' to proceed: ";
    
    $confirmation = trim(fgets(STDIN));
    
    if (strtolower($confirmation) !== 'yes') {
        echo "Operation cancelled.\n";
        exit(0);
    }
    
    // Delete all PayPal payment methods for user 1
    $deleteStmt = $pdo->prepare("DELETE FROM payment_methods WHERE user_id = 1 AND processor = 'paypal'");
    
    if ($deleteStmt->execute()) {
        $deletedCount = $deleteStmt->rowCount();
        echo "✅ Deleted $deletedCount PayPal payment method(s)\n\n";
        
        echo "🎯 Next Steps:\n";
        echo "1. Go to your frontend billing page\n";
        echo "2. Click 'Add PayPal Payment Method'\n";
        echo "3. You should be redirected to PayPal sandbox\n";
        echo "4. Log in with your sandbox account and approve\n";
        echo "5. You'll be redirected back and the vaulting should complete automatically\n";
        echo "6. Then test the automatic payment script again\n\n";
        
        echo "Make sure you're using a valid PayPal sandbox account for testing!\n";
        
    } else {
        echo "❌ Failed to delete PayPal payment methods\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?> 