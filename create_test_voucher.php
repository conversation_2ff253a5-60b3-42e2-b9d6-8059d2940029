<?php
require_once "mysql.php";

// Include the generateRandom function
function generateRandom($length = 10) {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

// Copy the process_referral_voucher function here
function process_referral_voucher($pdo, $invoice_id) {
    try {
        echo "=== PROCESSING REFERRAL VOUCHER for invoice $invoice_id ===\n";
        
        // Get invoice details
        $invoiceQuery = "SELECT * FROM invoices WHERE id = :invoice_id";
        $invoiceStmt = $pdo->prepare($invoiceQuery);
        $invoiceStmt->bindValue(":invoice_id", $invoice_id);
        $invoiceStmt->execute();
        $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$invoice) {
            echo "❌ Invoice not found for ID: $invoice_id\n";
            return false;
        }
        
        $user_id = $invoice['user_id'];
        echo "✅ Invoice found - user_id: $user_id, invoice_id: $invoice_id\n";
        echo "📋 Invoice data: " . json_encode($invoice) . "\n";
        
        // Eligibility Check 1: Invoice must not have been paid using a voucher
        if (isset($invoice['voucher']) && $invoice['voucher'] > 0) {
            echo "❌ Skipped - Invoice was paid using a voucher (amount: {$invoice['voucher']})\n";
            return false;
        }
        echo "✅ Check 1 passed - No voucher used\n";
        
        // Eligibility Check 2: Invoice must not be for adding credit
        $description = strtolower($invoice['description'] ?? '');
        echo "📝 Invoice description: '$description'\n";
        if (strpos($description, 'credit') !== false || 
            strpos($description, 'balance') !== false ||
            strpos($description, 'account credit') !== false ||
            $invoice['type'] === 'Credit Purchase') {
            echo "❌ Skipped - Invoice is for credit addition\n";
            return false;
        }
        echo "✅ Check 2 passed - Not a credit purchase\n";
        
        // Get the user's referrer
        $referrerQuery = "SELECT referrer_id FROM users WHERE id = :user_id";
        $referrerStmt = $pdo->prepare($referrerQuery);
        $referrerStmt->bindValue(":user_id", $user_id);
        $referrerStmt->execute();
        $referrerData = $referrerStmt->fetch(PDO::FETCH_ASSOC);
        
        echo "👤 User referrer data: " . json_encode($referrerData) . "\n";
        
        if (!$referrerData || !$referrerData['referrer_id']) {
            echo "❌ Skipped - User has no referrer (referrer_id is null)\n";
            return false;
        }
        
        $referrer_id = $referrerData['referrer_id'];
        echo "✅ Check 3 passed - Found referrer ID: $referrer_id for user ID: $user_id\n";
        
        // Determine payment method and calculate 5% reward for specific processors
        $payment_amount = 0;
        $payment_processor = null;
        
        // Get the latest transaction for this invoice to determine payment method
        $transactionQuery = "SELECT * FROM invoice_transactions WHERE invoice_id = :invoice_id ORDER BY date DESC LIMIT 1";
        $transactionStmt = $pdo->prepare($transactionQuery);
        $transactionStmt->bindValue(":invoice_id", $invoice_id);
        $transactionStmt->execute();
        $transaction = $transactionStmt->fetch(PDO::FETCH_ASSOC);
        
        echo "💳 Transaction data: " . json_encode($transaction) . "\n";
        
        if ($transaction) {
            $payment_processor = strtolower($transaction['processor'] ?? $transaction['payment_method'] ?? '');
            $payment_amount = floatval($transaction['amount'] ?? 0);
            echo "✅ Found transaction - processor: '$payment_processor', amount: $payment_amount\n";
        } else {
            // Fallback to invoice amount if no transaction found
            $payment_amount = floatval($invoice['total'] ?? $invoice['subtotal'] ?? 0);
            echo "⚠️  No transaction found, using invoice amount: $payment_amount\n";
        }
        
        // Only reward for PayPal, Paddle, CoinGate, and Stripe payments (5% of payment received)
        $eligible_processors = ['paypal', 'paddle', 'coingate', 'stripe'];
        echo "🔍 Checking processor '$payment_processor' against eligible: " . implode(', ', $eligible_processors) . "\n";
        
        if (!in_array($payment_processor, $eligible_processors)) {
            echo "❌ Skipped - Payment processor '$payment_processor' not eligible for referral rewards\n";
            return false;
        }
        echo "✅ Check 4 passed - Payment processor '$payment_processor' is eligible\n";
        
        // Calculate 5% voucher value
        $voucher_value = round($payment_amount * 0.05, 2);
        
        if ($voucher_value <= 0) {
            echo "❌ Skipped - Calculated voucher value is 0 or negative: $voucher_value\n";
            return false;
        }
        
        echo "✅ Check 5 passed - Calculated referral voucher value: €$voucher_value (5% of €$payment_amount)\n";
        
        // Begin transaction for voucher creation
        $pdo->beginTransaction();
        
        try {
            // Generate unique voucher code
            $voucher_code = "REF" . strtoupper(generateRandom(8));
            
            // Set expiry date (6 months from now)
            $expiry_date = date("Y-m-d H:i:s", strtotime("+6 months"));
            
            echo "🎫 Creating voucher - code: $voucher_code, user_id: $referrer_id, value: €$voucher_value\n";
            
            // Create the referral voucher
            $voucherQuery = "INSERT INTO vouchers (
                voucher_code, user_id, value, generated_by, status, 
                created_date, expiry_date, invoice_id, payment_processor
            ) VALUES (
                :voucher_code, :user_id, :value, :generated_by, 'active',
                NOW(), :expiry_date, :invoice_id, :payment_processor
            )";
            
            $voucherStmt = $pdo->prepare($voucherQuery);
            $voucherStmt->bindValue(":voucher_code", $voucher_code);
            $voucherStmt->bindValue(":user_id", $referrer_id); // Voucher belongs to the referrer
            $voucherStmt->bindValue(":value", $voucher_value);
            $voucherStmt->bindValue(":generated_by", $user_id); // Generated by the referred user's payment
            $voucherStmt->bindValue(":expiry_date", $expiry_date);
            $voucherStmt->bindValue(":invoice_id", $invoice_id);
            $voucherStmt->bindValue(":payment_processor", $payment_processor);
            
            $voucherStmt->execute();
            $voucher_id = $pdo->lastInsertId();
            
            echo "✅ Voucher created with ID: $voucher_id\n";
            
            // Log the voucher creation in activity log
            $logQuery = "INSERT INTO activity_log (
                user_id, action, description, user_name, activity_type
            ) VALUES (
                :user_id, 'referral_voucher_created', :description, :user_name, 'referral'
            )";
            
            $logStmt = $pdo->prepare($logQuery);
            $logStmt->bindValue(":user_id", $referrer_id);
            $logStmt->bindValue(":description", 
                "Referral voucher ($voucher_code) worth €$voucher_value created from invoice #$invoice_id payment via $payment_processor"
            );
            $logStmt->bindValue(":user_name", "User " . $referrer_id);
            $logStmt->execute();
            
            // Commit the transaction
            $pdo->commit();
            
            echo "🎉 SUCCESS: Created referral voucher ID: $voucher_id, Code: $voucher_code, Value: €$voucher_value for referrer ID: $referrer_id\n";
            
            return true;
            
        } catch (Exception $e) {
            $pdo->rollBack();
            echo "❌ ERROR creating voucher: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
            return false;
        }
        
    } catch (Exception $e) {
        echo "❌ ERROR in process_referral_voucher: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
        return false;
    }
}

echo "=== MANUAL REFERRAL VOUCHER CREATION TEST ===\n";

// Test with invoice 1037 that passed all checks
$invoice_id = 1037;

echo "Attempting to create referral voucher for invoice $invoice_id...\n\n";

// Call the function
$result = process_referral_voucher($pdo, $invoice_id);

if ($result) {
    echo "\n✅ SUCCESS: Referral voucher created!\n";
    
    // Check if voucher was actually created
    $checkVoucherQuery = "SELECT * FROM vouchers WHERE invoice_id = :invoice_id ORDER BY created_date DESC LIMIT 1";
    $stmt = $pdo->prepare($checkVoucherQuery);
    $stmt->bindValue(":invoice_id", $invoice_id);
    $stmt->execute();
    $voucher = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($voucher) {
        echo "🎫 Voucher found in database: " . json_encode($voucher) . "\n";
    } else {
        echo "❌ No voucher found in database despite success return\n";
    }
} else {
    echo "\n❌ FAILED: Referral voucher creation failed\n";
}

// Check current voucher count
$countQuery = "SELECT COUNT(*) as count FROM vouchers WHERE voucher_code LIKE 'REF%'";
$stmt = $pdo->prepare($countQuery);
$stmt->execute();
$count = $stmt->fetch(PDO::FETCH_ASSOC);

echo "\nTotal REF vouchers in database: {$count['count']}\n";
?> 