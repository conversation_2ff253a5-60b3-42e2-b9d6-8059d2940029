<?php
/**
 * Simple API Test for PayPal Vaulting Confirmation
 */

echo "Testing PayPal vaulting API endpoint...\n\n";

// Test with a simple request to see if endpoint is reachable
$api_url = 'http://localhost/New_client/api.php?f=confirm_paypal_vaulting';

// Test with dummy data
$post_data = json_encode([
    'token' => 'test_setup_token',
    'user_token' => 'test_user_token'
]);

echo "Making API call to: $api_url\n";
echo "POST data: $post_data\n\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($post_data)
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $http_code\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n\n";

if ($http_code == 200) {
    $data = json_decode($response, true);
    if (isset($data['error'])) {
        echo "✅ API endpoint is reachable (returned error as expected)\n";
        echo "Error: " . $data['error'] . "\n";
    } else {
        echo "✅ API endpoint is working\n";
    }
} else {
    echo "❌ API endpoint not reachable or server error\n";
}

// Now check the error logs
echo "\n=== Checking recent error logs ===\n";
if (file_exists('/var/log/apache2/error.log')) {
    echo "Last 10 lines of Apache error log:\n";
    system('tail -10 /var/log/apache2/error.log | grep -i paypal');
} else if (file_exists('/tmp/api_debug.log')) {
    echo "Last 10 lines of API debug log:\n";
    system('tail -10 /tmp/api_debug.log');
} else {
    echo "No error logs found in common locations\n";
}
?> 