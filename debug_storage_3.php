<?php
// Debug storage_id = 3 (6x1TB SSD) specifically
require_once 'mysql.php';

echo "=== Debugging Storage ID = 3 (6x1TB SSD) ===\n\n";

try {
    // Check what storage_id = 3 maps to
    $storage_query = $pdo->prepare("SELECT * FROM dedicated_storages WHERE id = 3");
    $storage_query->execute();
    $storage_result = $storage_query->fetch(PDO::FETCH_ASSOC);
    
    if ($storage_result) {
        echo "Storage ID 3 details:\n";
        echo "- Name: {$storage_result['name']}\n";
        echo "- Price: {$storage_result['price']}\n\n";
        
        $storage_name = $storage_result['name'];
        
        // Parse the storage name like the API does
        if (preg_match('/^(\d+)x(.+)$/', $storage_name, $matches)) {
            $required_qty = intval($matches[1]);
            $storage_type = trim($matches[2]);
            echo "Parsed storage requirements:\n";
            echo "- Required quantity: $required_qty\n";
            echo "- Storage type: '$storage_type'\n\n";
            
            // Check if this storage type exists in the storage table
            $storage_lookup = $pdo->prepare("SELECT id FROM storage WHERE name = ?");
            $storage_lookup->execute([$storage_type]);
            $storage_lookup_result = $storage_lookup->fetch(PDO::FETCH_ASSOC);
            
            if ($storage_lookup_result) {
                $required_storage_id = $storage_lookup_result['id'];
                echo "Found storage type in storage table:\n";
                echo "- Storage type ID: $required_storage_id\n\n";
                
                // Now check if any servers have this exact configuration
                echo "=== Checking Server Inventory ===\n";
                $cpu_id = 1; // Dual Intel Xeon E5-2630v3
                $country_id = 1; // Romania
                $required_bandwidth_speed = 1000; // 1 Gbps
                
                $exact_match_query = $pdo->prepare("
                    SELECT id, label, bay1, bay2, bay3, bay4, bay5, bay6, bay7, bay8, bay9, bay10,
                           port1_speed, port2_speed,
                           (CASE WHEN bay1 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay2 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay3 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay4 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay5 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay6 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay7 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay8 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay9 = ? THEN 1 ELSE 0 END) +
                           (CASE WHEN bay10 = ? THEN 1 ELSE 0 END) as matching_bays,
                           (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
                    FROM inventory_dedicated_servers
                    WHERE status = 'Available'
                    AND country_id = ?
                    AND order_id IS NULL
                    AND cpu = ?
                ");
                
                $params = array_fill(0, 10, $required_storage_id);
                $params[] = $country_id;
                $params[] = $cpu_id;
                
                $exact_match_query->execute($params);
                
                echo "Servers with CPU ID $cpu_id in country $country_id:\n";
                while ($server = $exact_match_query->fetch(PDO::FETCH_ASSOC)) {
                    echo "Server {$server['id']} ({$server['label']}):\n";
                    echo "  Bays: ";
                    for ($i = 1; $i <= 10; $i++) {
                        $bay = $server["bay$i"];
                        if ($bay) {
                            echo "bay$i=$bay ";
                        }
                    }
                    echo "\n";
                    echo "  Matching bays: {$server['matching_bays']} (need $required_qty)\n";
                    echo "  Port speeds: {$server['port1_speed']} + {$server['port2_speed']} = {$server['total_port_speed']} (need $required_bandwidth_speed)\n";
                    echo "  Exact match: " . (($server['matching_bays'] == $required_qty && $server['total_port_speed'] >= $required_bandwidth_speed) ? "YES" : "NO") . "\n\n";
                }
                
                // Count exact matches
                $count_query = $pdo->prepare("
                    SELECT COUNT(*) as count
                    FROM (
                        SELECT id,
                        (CASE WHEN bay1 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay2 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay3 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay4 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay5 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay6 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay7 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay8 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay9 = ? THEN 1 ELSE 0 END) +
                        (CASE WHEN bay10 = ? THEN 1 ELSE 0 END) as matching_bays,
                        (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
                        FROM inventory_dedicated_servers
                        WHERE status = 'Available'
                        AND country_id = ?
                        AND order_id IS NULL
                        AND cpu = ?
                    ) subquery
                    WHERE matching_bays = ? AND total_port_speed >= ?
                ");
                
                $count_params = array_fill(0, 10, $required_storage_id);
                $count_params[] = $country_id;
                $count_params[] = $cpu_id;
                $count_params[] = $required_qty;
                $count_params[] = $required_bandwidth_speed;
                
                $count_query->execute($count_params);
                $exact_count = $count_query->fetch(PDO::FETCH_ASSOC)['count'];
                
                echo "=== RESULT ===\n";
                echo "Exact matches found: $exact_count\n";
                echo "This explains why you're seeing '15 minutes' delivery time!\n\n";
                
                if ($exact_count > 0) {
                    echo "The system found $exact_count servers that exactly match:\n";
                    echo "- CPU: $cpu_id (Dual Intel Xeon E5-2630v3)\n";
                    echo "- Storage: $required_qty x storage type $required_storage_id ($storage_type)\n";
                    echo "- Bandwidth: >= $required_bandwidth_speed Mbps\n";
                    echo "- Location: Country $country_id (Romania)\n\n";
                    echo "That's why it shows 'green' stock color and '15 minutes' delivery.\n";
                } else {
                    echo "No exact matches found, should trigger upgrade scenario.\n";
                }
                
            } else {
                echo "Storage type '$storage_type' NOT found in storage table!\n";
                echo "This might be why the forced upgrade scenario isn't working.\n";
            }
        } else {
            echo "Could not parse storage name: '$storage_name'\n";
        }
    } else {
        echo "Storage ID 3 not found in dedicated_storages table!\n";
    }
    
    // Also show what's in the storage table
    echo "\n=== Storage Table Contents ===\n";
    $storage_table_query = $pdo->prepare("SELECT * FROM storage ORDER BY id");
    $storage_table_query->execute();
    while ($storage = $storage_table_query->fetch(PDO::FETCH_ASSOC)) {
        echo "Storage ID: {$storage['id']}, Name: '{$storage['name']}'\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
