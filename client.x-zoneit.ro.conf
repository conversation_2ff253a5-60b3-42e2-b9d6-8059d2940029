<VirtualHost *:443>
    ServerName client.x-zoneit.ro

    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem
    SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key

    DocumentRoot /var/www/html/New

    <Directory /var/www/html/New>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    # Enable mod_headers and mod_proxy_wstunnel
    LoadModule headers_module modules/mod_headers.so
    LoadModule proxy_module modules/mod_proxy.so
    LoadModule proxy_http_module modules/mod_proxy_http.so
    LoadModule proxy_wstunnel_module modules/mod_proxy_wstunnel.so

    # HTTPS headers
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # CSP header with development WebSocket allowance
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.paddle.com https://js.paddle.com; frame-src 'self' https://buy.paddle.com https://checkout.paddle.com; connect-src 'self' ws://0.0.0.0:3001 ws://localhost:3001 wss://client.x-zoneit.ro https://checkout.paddle.com https://checkout-service.paddle.com https://vendors.paddle.com; img-src 'self' data: https:; style-src 'self' 'unsafe-inline'; font-src 'self' data:"

    # WebSocket proxy for development server
    ProxyPass /ws ws://localhost:3001/ws
    ProxyPassReverse /ws ws://localhost:3001/ws
    
    # Upgrade WebSocket connections
    RewriteEngine On
    RewriteCond %{HTTP:Upgrade} websocket [NC]
    RewriteCond %{HTTP:Connection} upgrade [NC]
    RewriteRule ^/?(.*) "ws://localhost:3001/$1" [P,L]

    # Allow Apache to handle PHP files directly (exclude .php from proxy)
    ProxyPassMatch ^/(?!.*\.php)(.*)$ http://localhost:3001/$1
    ProxyPassReverse / http://localhost:3001/

    ProxyPreserveHost On
    RequestHeader set X-Forwarded-Proto "https"
    RequestHeader set X-Forwarded-Port "443"

    # Enable WebSocket support
    ProxyRequests Off
    ProxyPreserveHost On

    LogLevel debug
    ErrorLog ${APACHE_LOG_DIR}/test-error.log
    CustomLog ${APACHE_LOG_DIR}/test-access.log combined
</VirtualHost>

# Also add HTTP to HTTPS redirect
<VirtualHost *:80>
    ServerName client.x-zoneit.ro
    Redirect permanent / https://client.x-zoneit.ro/
</VirtualHost> 