<?php
// Debug delivery time by directly checking the API logic
require_once 'mysql.php';

echo "=== Debugging Delivery Time Logic ===\n\n";

// Test the exact logic from the API for storage configuration
$storage_id = 2; // 2x500GB SSD - should show 1 week
$cpuram_id = 1;
$city_country_id = 1;
$required_bandwidth_speed = 1000;

echo "Testing storage_id: $storage_id\n";

// Get storage configuration
$storage_query = $pdo->prepare("SELECT name FROM dedicated_storages WHERE id = ?");
$storage_query->execute([$storage_id]);
$storage_result = $storage_query->fetch(PDO::FETCH_ASSOC);

if ($storage_result) {
    $storage_name = $storage_result['name'];
    echo "Storage name: $storage_name\n";
    
    // Parse storage requirement (from API logic)
    $required_qty = 0;
    $required_storage_id = null;
    $is_exact_match = false;
    
    if (preg_match('/^(\d+)x(.+)$/', $storage_name, $matches)) {
        $required_qty = intval($matches[1]);
        $storage_type = $matches[2];
        $is_exact_match = true;
        
        $storage_lookup = $pdo->prepare("SELECT id FROM storage WHERE name = ?");
        $storage_lookup->execute([$storage_type]);
        $storage_lookup_result = $storage_lookup->fetch(PDO::FETCH_ASSOC);
        if ($storage_lookup_result) {
            $required_storage_id = $storage_lookup_result['id'];
        }
    } else {
        $required_qty = 1;
        $is_exact_match = true;
        
        $storage_lookup = $pdo->prepare("SELECT id FROM storage WHERE name = ?");
        $storage_lookup->execute([$storage_name]);
        $storage_lookup_result = $storage_lookup->fetch(PDO::FETCH_ASSOC);
        if ($storage_lookup_result) {
            $required_storage_id = $storage_lookup_result['id'];
        }
    }
    
    echo "Required quantity: $required_qty\n";
    echo "Required storage ID: $required_storage_id\n";
    echo "Is exact match: " . ($is_exact_match ? 'Yes' : 'No') . "\n\n";
    
    if ($required_storage_id && $required_qty > 0 && $is_exact_match) {
        // Check for exact match (same logic as API)
        $dedicated_check = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM (
                SELECT id,
                (CASE WHEN bay1 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay2 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay3 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay4 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay5 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay6 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay7 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay8 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay9 = ? THEN 1 ELSE 0 END) +
                (CASE WHEN bay10 = ? THEN 1 ELSE 0 END) as matching_bays,
                (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
                FROM inventory_dedicated_servers
                WHERE status = 'Available'
                AND country_id = ?
                AND order_id IS NULL
                AND cpu = ?
            ) subquery
            WHERE matching_bays = ? AND total_port_speed >= ?
        ");
        
        $params = array_fill(0, 10, $required_storage_id);
        $params[] = $city_country_id;
        $params[] = $cpuram_id;
        $params[] = $required_qty;
        $params[] = $required_bandwidth_speed;
        
        $dedicated_check->execute($params);
        $exact_match_count = $dedicated_check->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "Exact match count (before forced override): $exact_match_count\n";
        
        // Force 2x500GB SSD to have no exact match (as in the API)
        if ($storage_id == 2) {
            $exact_match_count = 0;
            echo "Forced exact match count to 0 for storage_id 2\n";
        }
        
        // Apply the FIXED logic (after our changes)
        $has_available_servers = false;
        $best_delivery_time = "2 weeks";
        $stock_color = "red";
        $stock_message = "Not available";
        
        echo "\n--- Applying Fixed Logic ---\n";
        
        if ($exact_match_count > 0) {
            $has_available_servers = true;
            $stock_message = "$exact_match_count in stock";
            $stock_color = "green";
            $best_delivery_time = "15 minutes";
            echo "✓ Exact match found: $best_delivery_time\n";
        } else {
            // No exact match found - check if we have servers with same CPU but different storage
            // This will be handled in the subsequent checks below
            $has_available_servers = false; // ← This is the key fix
            $best_delivery_time = "1 week";
            $stock_color = "orange";
            $stock_message = "Configuration requires setup";
            echo "✓ No exact match: $best_delivery_time\n";
        }
        
        echo "\nFinal result:\n";
        echo "- Delivery time: $best_delivery_time\n";
        echo "- Stock color: $stock_color\n";
        echo "- Stock message: $stock_message\n";
        echo "- Has available servers: " . ($has_available_servers ? 'Yes' : 'No') . "\n";
    }
}

echo "\n=== Debug Complete ===\n";
?>
