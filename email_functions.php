<?php
/**
 * Functions for sending invoice-related emails
 */

// Include necessary files
require_once("mysql.php");

// Import PHPMailer classes if not already imported
use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

/**
 * Send an email notification for a newly generated invoice
 *
 * @param int $invoice_id The ID of the invoice
 * @return bool True if email was sent successfully, false otherwise
 */
function sendInvoiceGeneratedEmail($invoice_id) {
    global $pdo;

    try {
        // Get invoice details
        $invoiceStmt = $pdo->prepare("
            SELECT i.*, u.email, u.first_name, u.last_name, u.company_name
            FROM invoices i
            JOIN users u ON i.user_id = u.id
            WHERE i.id = :invoice_id
        ");
        $invoiceStmt->bindValue(':invoice_id', $invoice_id);
        $invoiceStmt->execute();

        $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

        if (!$invoice) {
            error_log("Error sending invoice email: Invoice #$invoice_id not found");
            return false;
        }

        // Check if user has an email
        if (empty($invoice['email'])) {
            error_log("Error sending invoice email: No email address found for user ID " . $invoice['user_id']);
            return false;
        }

        // Get invoice items
        $itemsStmt = $pdo->prepare("
            SELECT * FROM invoice_items WHERE invoice_id = :invoice_id
        ");
        $itemsStmt->bindValue(':invoice_id', $invoice_id);
        $itemsStmt->execute();

        $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Get email template
        $templateStmt = $pdo->prepare("
            SELECT * FROM email_templates WHERE type = 'invoice_generated'
        ");
        $templateStmt->execute();

        if ($templateStmt->rowCount() == 0) {
            error_log("Error sending invoice email: 'invoice_generated' template not found");
            return false;
        }

        $template = $templateStmt->fetch(PDO::FETCH_ASSOC);

        // Prepare template variables
        $clientName = !empty($invoice['company_name']) ?
            $invoice['company_name'] :
            trim($invoice['first_name'] . ' ' . $invoice['last_name']);

        // Format invoice number based on type
        $invoiceNum = !empty($invoice['invoice_number']) ?
            $invoice['invoice_number'] :
            (!empty($invoice['proforma_number']) ? $invoice['proforma_number'] : $invoice_id);

        // For display in the email body, we might want to show the P- prefix for proforma numbers
        $displayInvoiceNum = !empty($invoice['invoice_number']) ?
            $invoice['invoice_number'] :
            (!empty($invoice['proforma_number']) ? 'P-' . $invoice['proforma_number'] : $invoice_id);

        // Format dates
        $invoiceDateCreated = date('Y-m-d', strtotime($invoice['date']));
        $invoiceDateDue = date('Y-m-d', strtotime($invoice['due_date']));

        // Format currency with HTML entity for Euro
        $invoiceTotal = '&euro;' . number_format($invoice['value'], 2);

        // Generate HTML for invoice items
        $invoiceHtmlContents = '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $invoiceHtmlContents .= '<tr style="background-color: #f8f9fa;">';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Description</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Quantity</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Unit Price</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Total</th>';
        $invoiceHtmlContents .= '</tr>';

        foreach ($items as $item) {
            $invoiceHtmlContents .= '<tr>';
            $invoiceHtmlContents .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . htmlspecialchars($item['description']) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">' . htmlspecialchars($item['quantity']) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($item['unit_price'], 2) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($item['total'], 2) . '</td>';
            $invoiceHtmlContents .= '</tr>';
        }

        // Add subtotal, tax, and total rows
        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;"><strong>Subtotal:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($invoice['subtotal'], 2) . '</td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;"><strong>Tax:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($invoice['tax'], 2) . '</td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right;"><strong>Total:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right;"><strong>&euro;' . number_format($invoice['value'], 2) . '</strong></td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '</table>';

        // Get invoice link from database settings
        try {
            // First check if the general_settings table exists
            $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'general_settings'");
            $tableExists = $tableCheckStmt->rowCount() > 0;

            if ($tableExists) {
                $invoiceLinkStmt = $pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'invoice_page_url' LIMIT 1");
                $invoiceLinkStmt->execute();

                if ($invoiceLinkStmt->rowCount() > 0) {
                    $invoiceLink = $invoiceLinkStmt->fetchColumn();
                } else {
                    // Fallback to default if setting not found
                    $invoiceLink = 'https://' . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'example.com') . '/client/invoices';
                }
            } else {
                // Table doesn't exist, use default
                $invoiceLink = 'https://' . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'example.com') . '/client/invoices';
            }
        } catch (Exception $e) {
            // If any error occurs, use default
            error_log("Error fetching invoice link from general_settings: " . $e->getMessage());
            $invoiceLink = 'https://' . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'example.com') . '/client/invoices';
        }

        // Replace template variables
        $subject = $template['subject'];
        $content = $template['content'];

        // Replace variables in subject
        $subject = str_replace('{$invoice_num}', $invoiceNum, $subject);

        // Replace variables in content
        $content = str_replace('{$client_name}', $clientName, $content);
        $content = str_replace('{$invoice_date_created}', $invoiceDateCreated, $content);
        $content = str_replace('{$invoice_payment_method}', $invoice['payment_method'], $content);
        $content = str_replace('{$invoice_num}', $displayInvoiceNum, $content);
        $content = str_replace('{$invoice_total}', $invoiceTotal, $content);
        $content = str_replace('{$invoice_date_due}', $invoiceDateDue, $content);
        $content = str_replace('{$invoice_html_contents}', $invoiceHtmlContents, $content);
        $content = str_replace('{$invoice_link}', $invoiceLink, $content);

        // Send the email - direct implementation for CLI compatibility
        try {
            // Get email settings from database
            $settingsStmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

            if ($settingsStmt->rowCount() == 0) {
                error_log("Email settings not configured");
                return false;
            }

            $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);

            // Check if email sending is enabled
            if (!$settings['enabled']) {
                error_log("Email sending is disabled in settings");
                return false;
            }

            // For Google provider, always set the Gmail SMTP settings
            if ($settings['provider'] === 'google') {
                $settings['smtp_host'] = 'smtp.gmail.com';
                $settings['smtp_port'] = '587';
                $settings['smtp_encryption'] = 'tls';
            }

            // Set sender details
            $fromName = $settings['from_name'] ? $settings['from_name'] : 'Admin System';
            $fromEmail = $settings['from_email'] ? $settings['from_email'] : '<EMAIL>';
            $replyTo = !empty($settings['reply_to']) ? $settings['reply_to'] : '';

            // Check if signature is enabled and add it to the message
            $useHtmlMessage = true;
            $htmlMessage = $content;

            if (!empty($settings['use_signature']) && !empty($settings['signature'])) {
                // Add the signature with a divider
                $htmlMessage .= "<hr style='margin-top: 20px; margin-bottom: 20px; border: 0; border-top: 1px solid #eee;'>";
                $htmlMessage .= $settings['signature'];
            }

            // Create a new PHPMailer instance
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/SMTP.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/Exception.php';
            $mail = new PHPMailer(true);

            // Server settings
            $mail->SMTPDebug = SMTP::DEBUG_OFF; // Disable debug output

            // Always use SMTP
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = $settings['smtp_password'];

            // Set encryption type
            if ($settings['smtp_encryption'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 465;
            } else {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // TLS
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 587;
            }

            // Recipients
            $mail->setFrom($fromEmail, $fromName);
            $mail->addAddress($invoice['email']);

            if ($replyTo) {
                $mail->addReplyTo($replyTo);
            }

            // Content
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8'; // Set UTF-8 character encoding
            $mail->Subject = $subject;
            $mail->Body = $htmlMessage;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $content)); // Plain text alternative

            // Send the email
            $mail->send();
            $result = true;
        } catch (Exception $mailException) {
            error_log("Error sending email: " . $mailException->getMessage());
            $result = false;
        }

        if ($result) {
            error_log("Invoice email sent successfully to " . $invoice['email'] . " for invoice #$invoice_id");

            // Log to activity log
            $activityStmt = $pdo->prepare("
                INSERT INTO activity_log (
                    user_id,
                    action,
                    description,
                    user_name,
                    timestamp,
                    activity_type,
                    invoice_number,
                    proforma_number
                ) VALUES (
                    :user_id,
                    'Invoice Email Sent',
                    :description,
                    :user_name,
                    NOW(),
                    'billing',
                    :invoice_number,
                    :proforma_number
                )
            ");

            $activityStmt->bindValue(':user_id', $invoice['user_id']);
            $activityStmt->bindValue(':description', "Invoice notification email sent to " . $invoice['email']);
            $activityStmt->bindValue(':user_name', $clientName);
            $activityStmt->bindValue(':invoice_number', $invoice['invoice_number'] ?? null);
            $activityStmt->bindValue(':proforma_number', $invoice['proforma_number'] ?? null);

            $activityStmt->execute();

            return true;
        } else {
            error_log("Failed to send invoice email to " . $invoice['email'] . " for invoice #$invoice_id");
            return false;
        }

    } catch (Exception $e) {
        error_log("Error in sendInvoiceGeneratedEmail: " . $e->getMessage());
        return false;
    }
}

/**
 * Send an email notification for a paid invoice
 *
 * @param int $invoice_id The ID of the invoice
 * @return bool True if email was sent successfully, false otherwise
 */
function sendInvoicePaidEmail($invoice_id) {
    global $pdo;

    try {
        // Get invoice details
        $invoiceStmt = $pdo->prepare("
            SELECT i.*, u.email, u.first_name, u.last_name, u.company_name
            FROM invoices i
            JOIN users u ON i.user_id = u.id
            WHERE i.id = :invoice_id
        ");
        $invoiceStmt->bindValue(':invoice_id', $invoice_id);
        $invoiceStmt->execute();

        $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

        if (!$invoice) {
            error_log("Error sending paid invoice email: Invoice #$invoice_id not found");
            return false;
        }

        // Check if user has an email
        if (empty($invoice['email'])) {
            error_log("Error sending paid invoice email: No email address found for user ID " . $invoice['user_id']);
            return false;
        }

        // Get invoice items
        $itemsStmt = $pdo->prepare("
            SELECT * FROM invoice_items WHERE invoice_id = :invoice_id
        ");
        $itemsStmt->bindValue(':invoice_id', $invoice_id);
        $itemsStmt->execute();

        $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Get email template
        $templateStmt = $pdo->prepare("
            SELECT * FROM email_templates WHERE type = 'invoice_paid'
        ");
        $templateStmt->execute();

        if ($templateStmt->rowCount() == 0) {
            error_log("Error sending paid invoice email: 'invoice_paid' template not found");
            return false;
        }

        $template = $templateStmt->fetch(PDO::FETCH_ASSOC);

        // Prepare template variables
        $clientName = !empty($invoice['company_name']) ?
            $invoice['company_name'] :
            trim($invoice['first_name'] . ' ' . $invoice['last_name']);

        // Format invoice number based on type
        $invoiceNum = !empty($invoice['invoice_number']) ?
            $invoice['invoice_number'] :
            (!empty($invoice['proforma_number']) ? $invoice['proforma_number'] : $invoice_id);

        // Format payment date
        $paymentDate = !empty($invoice['Paid_date']) ?
            date('Y-m-d', strtotime($invoice['Paid_date'])) :
            date('Y-m-d'); // Use current date if payment date is not set

        // Format currency with HTML entity for Euro
        $invoiceTotal = '&euro;' . number_format($invoice['value'], 2);

        // Generate HTML for invoice items
        $invoiceHtmlContents = '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $invoiceHtmlContents .= '<tr style="background-color: #f8f9fa;">';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Description</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Quantity</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Unit Price</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Total</th>';
        $invoiceHtmlContents .= '</tr>';

        foreach ($items as $item) {
            $invoiceHtmlContents .= '<tr>';
            $invoiceHtmlContents .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . htmlspecialchars($item['description']) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">' . htmlspecialchars($item['quantity']) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($item['unit_price'], 2) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($item['total'], 2) . '</td>';
            $invoiceHtmlContents .= '</tr>';
        }

        // Add subtotal, tax, and total rows
        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;"><strong>Subtotal:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($invoice['subtotal'], 2) . '</td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;"><strong>Tax:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($invoice['tax'], 2) . '</td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right;"><strong>Total:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right;"><strong>&euro;' . number_format($invoice['value'], 2) . '</strong></td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '</table>';

        // Replace template variables
        $subject = $template['subject'];
        $content = $template['content'];

        // Replace variables in subject
        $subject = str_replace('{$invoice_num}', $invoiceNum, $subject);

        // Replace variables in content
        $content = str_replace('{$client_name}', $clientName, $content);
        $content = str_replace('{$invoice_num}', $invoiceNum, $content);
        $content = str_replace('{$invoice_total}', $invoiceTotal, $content);
        $content = str_replace('{$payment_date}', $paymentDate, $content);
        $content = str_replace('{$invoice_html_contents}', $invoiceHtmlContents, $content);

        // Send the email - direct implementation for CLI compatibility
        try {
            // Get email settings from database
            $settingsStmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

            if ($settingsStmt->rowCount() == 0) {
                error_log("Email settings not configured");
                return false;
            }

            $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);

            // Check if email sending is enabled
            if (!$settings['enabled']) {
                error_log("Email sending is disabled in settings");
                return false;
            }

            // For Google provider, always set the Gmail SMTP settings
            if ($settings['provider'] === 'google') {
                $settings['smtp_host'] = 'smtp.gmail.com';
                $settings['smtp_port'] = '587';
                $settings['smtp_encryption'] = 'tls';
            }

            // Set sender details
            $fromName = $settings['from_name'] ? $settings['from_name'] : 'Admin System';
            $fromEmail = $settings['from_email'] ? $settings['from_email'] : '<EMAIL>';
            $replyTo = !empty($settings['reply_to']) ? $settings['reply_to'] : '';

            // Check if signature is enabled and add it to the message
            $useHtmlMessage = true;
            $htmlMessage = $content;

            if (!empty($settings['use_signature']) && !empty($settings['signature'])) {
                // Add the signature with a divider
                $htmlMessage .= "<hr style='margin-top: 20px; margin-bottom: 20px; border: 0; border-top: 1px solid #eee;'>";
                $htmlMessage .= $settings['signature'];
            }

            // Create a new PHPMailer instance
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/SMTP.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/Exception.php';
            $mail = new PHPMailer(true);

            // Server settings
            $mail->SMTPDebug = SMTP::DEBUG_OFF; // Disable debug output

            // Always use SMTP
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = $settings['smtp_password'];

            // Set encryption type
            if ($settings['smtp_encryption'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 465;
            } else {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // TLS
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 587;
            }

            // Recipients
            $mail->setFrom($fromEmail, $fromName);
            $mail->addAddress($invoice['email']);

            if ($replyTo) {
                $mail->addReplyTo($replyTo);
            }

            // Content
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8'; // Set UTF-8 character encoding
            $mail->Subject = $subject;
            $mail->Body = $htmlMessage;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $content)); // Plain text alternative

            // Send the email
            $mail->send();
            $result = true;
        } catch (Exception $mailException) {
            error_log("Error sending paid invoice email: " . $mailException->getMessage());
            $result = false;
        }

        if ($result) {
            error_log("Paid invoice email sent successfully to " . $invoice['email'] . " for invoice #$invoice_id");

            // Log to activity log
            $activityStmt = $pdo->prepare("
                INSERT INTO activity_log (
                    user_id,
                    action,
                    description,
                    user_name,
                    timestamp,
                    activity_type,
                    invoice_number,
                    proforma_number
                ) VALUES (
                    :user_id,
                    'Invoice Paid Email Sent',
                    :description,
                    :user_name,
                    NOW(),
                    'billing',
                    :invoice_number,
                    :proforma_number
                )
            ");

            $activityStmt->bindValue(':user_id', $invoice['user_id']);
            $activityStmt->bindValue(':description', "Payment confirmation email sent to " . $invoice['email']);
            $activityStmt->bindValue(':user_name', $clientName);
            $activityStmt->bindValue(':invoice_number', $invoice['invoice_number'] ?? null);
            $activityStmt->bindValue(':proforma_number', $invoice['proforma_number'] ?? null);

            $activityStmt->execute();

            return true;
        } else {
            error_log("Failed to send paid invoice email to " . $invoice['email'] . " for invoice #$invoice_id");
            return false;
        }

    } catch (Exception $e) {
        error_log("Error in sendInvoicePaidEmail: " . $e->getMessage());
        return false;
    }
}

/**
 * Send an email with server credentials when a cloud server is generated
 *
 * @param int $server_id The SolusVM server ID
 * @param int $user_id The ID of the user
 * @return bool True if email was sent successfully, false otherwise
 */
function sendServerDetailsEmail($server_id, $user_id) {
    global $pdo;

    try {
        // Get server details from orders_items table
        $serverStmt = $pdo->prepare("
            SELECT oi.*, o.owner_id as user_id
            FROM orders_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE oi.server_id = :server_id AND oi.type = 'cloud'
        ");
        $serverStmt->bindValue(':server_id', $server_id);
        $serverStmt->execute();

        $server = $serverStmt->fetch(PDO::FETCH_ASSOC);

        if (!$server) {
            error_log("Error sending server details email: Server #$server_id not found in orders_items");
            return false;
        }
        
        // Get IP address from SolusVM API (same as vps_server_details function)
        $main_ip = get_server_ip_from_solus($server_id);
        
        // Get user email
        $userStmt = $pdo->prepare("SELECT email, first_name, last_name FROM users WHERE id = :user_id");
        $userStmt->bindValue(':user_id', $server['user_id']);
        $userStmt->execute();
        
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            error_log("Error sending server details email: User #" . $server['user_id'] . " not found");
            return false;
        }
        
        // Get email template
        $templateStmt = $pdo->prepare("SELECT subject, content FROM email_templates WHERE type = 'server_details'");
        $templateStmt->execute();
        
        $template = $templateStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            error_log("Error sending server details email: Template 'server_details' not found");
            return false;
        }
        
        // Prepare email data
        $clientName = trim($user['first_name'] . ' ' . $user['last_name']);
        $subject = $template['subject'];
        $content = $template['content'];
        
        // Replace variables in subject
        $subject = str_replace('{hostname}', $server['hostname'] ?? 'Server #' . $server_id, $subject);
        $subject = str_replace('{client_name}', $clientName, $subject);
        
        // Replace variables in content
        $content = str_replace('{client_name}', $clientName, $content);
        $content = str_replace('{hostname}', $server['hostname'] ?? 'Server #' . $server_id, $content);
        $content = str_replace('{main_ip}', $main_ip, $content);
        $content = str_replace('{username}', 'root', $content);
        $content = str_replace('{password}', $server['password'] ?? 'N/A', $content);
        $content = str_replace('{server_id}', $server_id, $content);
        
        // Convert ¶ to <br> for HTML display
        $content = str_replace('¶', '<br>', $content);
        
        // Log email to database before sending
        $emailLogId = null;
        try {
            $logStmt = $pdo->prepare("
                INSERT INTO email_logs (to_email, subject, body, status, created_at) 
                VALUES (:to_email, :subject, :body, :status, NOW())
            ");
            $logStmt->bindValue(':to_email', $user['email']);
            $logStmt->bindValue(':subject', $subject);
            $logStmt->bindValue(':body', $content);
            $logStmt->bindValue(':status', 'pending');
            $logStmt->execute();
            
            $emailLogId = $pdo->lastInsertId();
            error_log("Email logged to database with ID: $emailLogId");
        } catch (Exception $e) {
            error_log("Failed to log email to database: " . $e->getMessage());
        }
        
        // Send email using PHPMailer with SMTP (same as other email functions)
        try {
            // Get email settings from database
            $settingsStmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

            if ($settingsStmt->rowCount() == 0) {
                error_log("Email settings not configured");
                return false;
            }

            $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);

            // Check if email sending is enabled
            if (!$settings['enabled']) {
                error_log("Email sending is disabled in settings");
                return false;
            }

            // For Google provider, always set the Gmail SMTP settings
            if ($settings['provider'] === 'google') {
                $settings['smtp_host'] = 'smtp.gmail.com';
                $settings['smtp_port'] = '587';
                $settings['smtp_encryption'] = 'tls';
            }

            // Set sender details
            $fromName = $settings['from_name'] ? $settings['from_name'] : 'ZetServers';
            $fromEmail = $settings['from_email'] ? $settings['from_email'] : '<EMAIL>';
            $replyTo = !empty($settings['reply_to']) ? $settings['reply_to'] : '';

            // Check if signature is enabled and add it to the message
            $htmlMessage = $content;

            if (!empty($settings['use_signature']) && !empty($settings['signature'])) {
                // Add the signature with a divider
                $htmlMessage .= "<hr style='margin-top: 20px; margin-bottom: 20px; border: 0; border-top: 1px solid #eee;'>";
                $htmlMessage .= $settings['signature'];
            }

            // Create a new PHPMailer instance
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/SMTP.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/Exception.php';
            $mail = new PHPMailer(true);

            // Server settings
            $mail->SMTPDebug = SMTP::DEBUG_OFF; // Disable debug output

            // Always use SMTP
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = $settings['smtp_password'];

            // Set encryption type
            if ($settings['smtp_encryption'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 465;
            } else {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // TLS
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 587;
            }

            // Recipients
            $mail->setFrom($fromEmail, $fromName);
            $mail->addAddress($user['email']);

            if ($replyTo) {
                $mail->addReplyTo($replyTo);
            }

            // Content
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8'; // Set UTF-8 character encoding
            $mail->Subject = $subject;
            $mail->Body = $htmlMessage;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $content)); // Plain text alternative

            // Send the email
            $mail->send();
            $mailSent = true;
        } catch (Exception $mailException) {
            error_log("Error sending server details email: " . $mailException->getMessage());
            $mailSent = false;
        }
        
        // Update email log with result (only if we successfully logged initially)
        if ($emailLogId) {
            try {
                $updateLogStmt = $pdo->prepare("
                    UPDATE email_logs 
                    SET status = :status, sent_at = :sent_at, error_message = :error_message
                    WHERE id = :id
                ");
                
                if ($mailSent) {
                    $updateLogStmt->bindValue(':status', 'sent');
                    $updateLogStmt->bindValue(':sent_at', date('Y-m-d H:i:s'));
                    $updateLogStmt->bindValue(':error_message', null);
                    $updateLogStmt->bindValue(':id', $emailLogId);
                    $updateLogStmt->execute();
                } else {
                    $updateLogStmt->bindValue(':status', 'failed');
                    $updateLogStmt->bindValue(':sent_at', null);
                    $updateLogStmt->bindValue(':error_message', 'Failed to send email using PHPMailer/SMTP');
                    $updateLogStmt->bindValue(':id', $emailLogId);
                    $updateLogStmt->execute();
                }
            } catch (Exception $e) {
                error_log("Failed to update email log: " . $e->getMessage());
            }
        }
        
        if ($mailSent) {
            error_log("Server details email sent successfully to {$user['email']} for server #$server_id" . ($emailLogId ? " (Log ID: $emailLogId)" : ""));
            return true;
        } else {
            error_log("Failed to send server details email to {$user['email']} for server #$server_id" . ($emailLogId ? " (Log ID: $emailLogId)" : ""));
            return false;
        }
        
    } catch (Exception $e) {
        error_log("Exception in sendServerDetailsEmail: " . $e->getMessage());
        return false;
    }
}

/**
 * Get server IP address from SolusVM API
 *
 * @param int $server_id The SolusVM server ID
 * @return string The main IP address or 'N/A' if not found
 */
function get_server_ip_from_solus($server_id) {
    try {
        // Same API configuration as vps_server_details function
        $apiKey = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        $baseUrl = "https://virt.zetservers.com";
        
        $apiEndpoint = $baseUrl . "/api/v1/servers";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiEndpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer " . $apiKey,
            "Accept: application/json",
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            error_log("cURL Error in get_server_ip_from_solus: " . curl_error($ch));
            curl_close($ch);
            return 'N/A';
        }

        curl_close($ch);

        // Process API response
        $apiData = json_decode($response, true);

        // Check if we got a valid response
        if ($httpCode != 200 || !$apiData || !isset($apiData["data"]) || empty($apiData["data"])) {
            error_log("SolusVM API Error in get_server_ip_from_solus: " . ($apiData["message"] ?? "Unknown error") . " (HTTP Code: $httpCode)");
            return 'N/A';
        }

        // Find the server with the matching ID and extract IP
        foreach ($apiData["data"] as $server) {
            if ((string) $server["id"] === (string) $server_id) {
                // Extract main IP address (same logic as vps_server_details)
                $main_ip = isset($server["ip_addresses"]["ipv4"][0])
                    ? $server["ip_addresses"]["ipv4"][0]["ip"]
                    : "N/A";
                
                error_log("Found IP address for server #$server_id: $main_ip");
                return $main_ip;
            }
        }
        
        error_log("Server #$server_id not found in SolusVM API response");
        return 'N/A';
        
    } catch (Exception $e) {
        error_log("Exception in get_server_ip_from_solus: " . $e->getMessage());
        return 'N/A';
    }
}


