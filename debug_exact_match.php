<?php
// Debug the exact match logic for 6x1TB SSD
require_once 'mysql.php';

echo "=== Debugging Exact Match Logic for 6x1TB SSD ===\n\n";

try {
    // Test parameters
    $cpu_id = 1; // Dual Intel Xeon E5-2630v3
    $storage_id = 3; // 6x1TB SSD
    $bandwidth_id = 1; // 1 Gbps
    $country_id = 1; // Romania
    $city_id = 1; // Bucharest
    
    echo "Test parameters:\n";
    echo "- CPU ID: $cpu_id\n";
    echo "- Storage ID: $storage_id\n";
    echo "- Bandwidth ID: $bandwidth_id\n";
    echo "- Country ID: $country_id\n";
    echo "- City ID: $city_id\n\n";
    
    // Get storage requirements like the API does
    $storage_query = $pdo->prepare("SELECT name FROM dedicated_storages WHERE id = ?");
    $storage_query->execute([$storage_id]);
    $storage_result = $storage_query->fetch(PDO::FETCH_ASSOC);
    
    if (!$storage_result) {
        echo "ERROR: Storage ID $storage_id not found!\n";
        exit;
    }
    
    $storage_name = $storage_result['name'];
    echo "Storage name: '$storage_name'\n";
    
    // Parse storage requirements
    if (preg_match('/^(\d+)x(.+)$/', $storage_name, $matches)) {
        $required_qty = intval($matches[1]);
        $storage_type = trim($matches[2]);
        echo "Parsed: $required_qty x '$storage_type'\n";
    } else {
        $required_qty = 1;
        $storage_type = $storage_name;
        echo "Single storage: '$storage_type'\n";
    }
    
    // Get storage type ID from storage table
    $storage_lookup = $pdo->prepare("SELECT id FROM storage WHERE name = ?");
    $storage_lookup->execute([$storage_type]);
    $storage_lookup_result = $storage_lookup->fetch(PDO::FETCH_ASSOC);
    
    if ($storage_lookup_result) {
        $required_storage_id = $storage_lookup_result['id'];
        echo "Storage type ID: $required_storage_id\n";
    } else {
        echo "ERROR: Storage type '$storage_type' not found in storage table!\n";
        exit;
    }
    
    // Get bandwidth requirements
    $bandwidth_query = $pdo->prepare("SELECT speed FROM dedicated_bandwidth WHERE id = ?");
    $bandwidth_query->execute([$bandwidth_id]);
    $bandwidth_result = $bandwidth_query->fetch(PDO::FETCH_ASSOC);
    $required_bandwidth_speed = $bandwidth_result ? intval($bandwidth_result['speed']) : 0;
    echo "Required bandwidth: $required_bandwidth_speed Mbps\n\n";
    
    // Now test the exact match query from the API
    echo "=== Testing Exact Match Query ===\n";
    
    $exact_match_query = $pdo->prepare("
        SELECT id, label, cpu, country_id, status, order_id,
               bay1, bay2, bay3, bay4, bay5, bay6, bay7, bay8, bay9, bay10,
               port1_speed, port2_speed,
               (CASE WHEN bay1 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay2 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay3 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay4 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay5 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay6 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay7 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay8 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay9 = ? THEN 1 ELSE 0 END) +
               (CASE WHEN bay10 = ? THEN 1 ELSE 0 END) as matching_bays,
               (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
        FROM inventory_dedicated_servers
        WHERE status = 'Available'
        AND country_id = ?
        AND order_id IS NULL
        AND cpu = ?
    ");
    
    $params = array_fill(0, 10, $required_storage_id);
    $params[] = $country_id;
    $params[] = $cpu_id;
    
    $exact_match_query->execute($params);
    
    echo "Servers matching CPU and location criteria:\n";
    $exact_matches = 0;
    while ($server = $exact_match_query->fetch(PDO::FETCH_ASSOC)) {
        echo "\nServer {$server['id']} ({$server['label']}):\n";
        echo "  CPU: {$server['cpu']}, Country: {$server['country_id']}, Status: {$server['status']}\n";
        echo "  Bays: ";
        for ($i = 1; $i <= 10; $i++) {
            $bay = $server["bay$i"];
            if ($bay) {
                echo "bay$i=$bay ";
            }
        }
        echo "\n";
        echo "  Matching bays: {$server['matching_bays']} (need $required_qty)\n";
        echo "  Port speeds: {$server['port1_speed']} + {$server['port2_speed']} = {$server['total_port_speed']} (need $required_bandwidth_speed)\n";
        
        $is_exact_match = ($server['matching_bays'] == $required_qty && $server['total_port_speed'] >= $required_bandwidth_speed);
        echo "  Is exact match: " . ($is_exact_match ? "YES" : "NO") . "\n";
        
        if ($is_exact_match) {
            $exact_matches++;
        }
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "Total exact matches found: $exact_matches\n";
    
    if ($exact_matches > 0) {
        echo "This explains why you're seeing '15 minutes' delivery and green status!\n";
        echo "The system thinks it found $exact_matches servers with exactly:\n";
        echo "- CPU ID $cpu_id\n";
        echo "- $required_qty x storage type $required_storage_id ($storage_type)\n";
        echo "- >= $required_bandwidth_speed Mbps bandwidth\n";
    } else {
        echo "No exact matches found - this should trigger partial match logic.\n";
    }
    
    // Also test the count query that the API actually uses
    echo "\n=== Testing API Count Query ===\n";
    $count_query = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT id,
            (CASE WHEN bay1 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay2 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay3 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay4 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay5 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay6 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay7 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay8 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay9 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay10 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay11 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay12 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay13 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay14 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay15 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay16 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay17 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay18 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay19 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay20 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay21 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay22 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay23 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay24 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay25 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay26 = ? THEN 1 ELSE 0 END) as matching_bays,
            (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
            FROM inventory_dedicated_servers
            WHERE status = 'Available'
            AND country_id = ?
            AND order_id IS NULL
            AND cpu = ?
        ) subquery
        WHERE matching_bays = ? AND total_port_speed >= ?
    ");
    
    $count_params = array_fill(0, 26, $required_storage_id);
    $count_params[] = $country_id;
    $count_params[] = $cpu_id;
    $count_params[] = $required_qty;
    $count_params[] = $required_bandwidth_speed;
    
    $count_query->execute($count_params);
    $api_count = $count_query->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "API count query result: $api_count\n";
    echo "This is the exact number the API is using for 'exact matches'.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
