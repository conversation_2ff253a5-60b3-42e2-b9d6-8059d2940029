<?php
// Include database connection
require_once("mysql.php");

// Get the VPS ID from the URL parameter
$vps_id = isset($_GET['id']) ? $_GET['id'] : null;

if (!$vps_id) {
    echo "Error: No VPS ID provided";
    exit;
}

try {
    // Check if this ID exists as orders_items.server_id
    $check_server_id = $pdo->prepare("
        SELECT oi.id
        FROM orders_items oi
        WHERE oi.server_id = :id AND (oi.type LIKE '%vps%' OR oi.type LIKE '%Vps%')
    ");
    $check_server_id->bindValue(':id', $vps_id);
    $check_server_id->execute();
    
    if ($check_server_id->rowCount() > 0) {
        $row = $check_server_id->fetch(PDO::FETCH_ASSOC);
        $correct_id = $row['id'];
        
        // Redirect to the correct URL
        header("Location: /zet/#/vps-services/$correct_id");
        exit;
    } else {
        // Check if this is already a valid orders_items.id
        $check_oi_id = $pdo->prepare("
            SELECT oi.id
            FROM orders_items oi
            WHERE oi.id = :id AND (oi.type LIKE '%vps%' OR oi.type LIKE '%Vps%')
        ");
        $check_oi_id->bindValue(':id', $vps_id);
        $check_oi_id->execute();
        
        if ($check_oi_id->rowCount() > 0) {
            // This is already a valid ID, redirect to the correct URL format
            header("Location: /zet/#/vps-services/$vps_id");
            exit;
        } else {
            echo "Error: VPS not found with ID $vps_id";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
