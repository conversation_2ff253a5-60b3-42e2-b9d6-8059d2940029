import React, { useEffect, useState, useRef } from "react"
import { Outlet, Link, useLocation, useNavigate, useParams } from "react-router-dom";
import DiscountAlert from "../components/DiscountAlert";

const Ticket = () => {
  const navigate = useNavigate(); 
  const { id: ticketId } = useParams();
  const [messages, setMessages] = useState([]);
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });
  const messageInputRef = useRef(null);
  const messagesEndRef = useRef(null);
  // Add file attachment state
  const fileInputRef = useRef(null);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [fileUploadError, setFileUploadError] = useState('');
  
  // Add access control state
  const [hasAccess, setHasAccess] = useState(null); // Start with null to show loading
  const [accessError, setAccessError] = useState('');
  
  // Function to get token from both storage locations
  function getToken() {
    const sessionToken = sessionStorage.getItem('token');
    const localToken = localStorage.getItem('token');
    return sessionToken || localToken;
  }

  // No Access Component
  const NoAccessMessage = () => (
    <div className="text-center p-5">
      <i className="fa fa-lock fa-4x text-danger mb-4"></i>
      <h3 className="text-danger mb-3">Access Denied</h3>
      <p className="text-muted mb-4" style={{fontSize: '1.1rem'}}>
        {accessError || 'You do not have permission to access this support ticket.'}
      </p>
      <p className="text-muted">
        Please contact your administrator if you believe this is an error.
      </p>
      <div className="mt-4">
        <button 
          className="btn btn-secondary me-2" 
          onClick={() => window.location.reload()}
        >
          <i className="fa fa-refresh me-1"></i> Refresh Page
        </button>
        <Link to="/support" className="btn btn-primary">
          <i className="fa fa-arrow-left me-1"></i> Back to Support
        </Link>
      </div>
    </div>
  );
  
  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Fetch ticket details
  const fetchTicketDetails = () => {
    const token = getToken();
    if (!token) {
      setNotification({
        show: true,
        message: 'Authentication required. Please log in.',
        type: 'error'
      });
      return;
    }
    
    // Pass ticket ID as URL parameter instead of in the request body
    fetch(`/api.php?f=ticket_details&id=${ticketId}`, {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => {
      console.log("Ticket Details Response status:", response.status);
      
      // Check for 403 status code BEFORE trying to parse JSON
      if (response.status === 403) {
        console.log("403 detected - setting no access for ticket details");
        setHasAccess(false);
        setAccessError('You do not have permission to access this support ticket');
        throw new Error('Access denied');
      }
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    })
    .then(data => {
      console.log("Ticket Details Response Data:", data);
      
      if (data.error) {
        console.error("Error fetching ticket:", data);
        if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
          setHasAccess(false);
          setAccessError(data.message || 'Access denied');
          return;
        }
        setNotification({
          show: true,
          message: data.message || 'Error loading ticket details',
          type: 'error'
        });
        return;
      }
      
      // Success
      setTicket(data);
      setHasAccess(true);
    })
    .catch(error => {
      console.error("Error fetching ticket details:", error);
      if (error.message !== 'Access denied') {
        setNotification({
          show: true,
          message: 'Network error loading ticket details',
          type: 'error'
        });
      }
    });
  };

  // Fetch messages
  const fetchMessages = () => {
    setLoading(true);
    const token = getToken();
    
    fetch(`/api.php?f=messages&id=${ticketId}`, {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => {
      console.log("Messages Response status:", response.status);
      
      // Check for 403 status code
      if (response.status === 403) {
        console.log("403 detected - setting no access for messages");
        setHasAccess(false);
        setAccessError('You do not have permission to access this support ticket');
        setLoading(false);
        throw new Error('Access denied');
      }
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    })
    .then(data => {
      console.log("Messages Response Data:", data);
      
      if (data.error) {
        if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
          setHasAccess(false);
          setAccessError(data.message || 'Access denied');
          setLoading(false);
          return;
        }
      }
      
      // Success - handle new response format with ticket status
      if (data.messages) {
        // New format with ticket status
        setMessages(data.messages);
        
        // Update ticket status if it has changed
        if (data.ticket_status && ticket && ticket.status !== data.ticket_status) {
          setTicket(prevTicket => ({
            ...prevTicket,
            status: data.ticket_status,
            last_reply: data.last_reply,
            last_reply_by: data.last_reply_by
          }));
        }
      } else {
        // Backward compatibility - old format (just messages array)
        setMessages(data);
      }
      
      setHasAccess(true);
      setLoading(false);
    })
    .catch(error => {
      console.error("Error fetching messages:", error);
      if (error.message !== 'Access denied') {
        // Handle other errors normally
      }
      setLoading(false);
    });
  };

  // Send a new message
  const sendMessage = () => {
    const messageText = messageInputRef.current.value.trim();
    if (!messageText && selectedFiles.length === 0) return;
    
    setSending(true);
    const token = getToken();
    
    if (!token) {
      setNotification({
        show: true,
        message: 'Authentication required. Please log in.',
        type: 'error'
      });
      setSending(false);
      return;
    }
    
    // Create FormData for file upload
    const formData = new FormData();
    formData.append('ticket_id', ticketId);
    formData.append('message', messageText || ''); // Allow empty message if files are attached
    formData.append('token', token);
    
    // Add files to FormData
    selectedFiles.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });
    
    fetch("/api.php?f=add_message", {
      method: "POST",
      body: formData // Don't set Content-Type header, let browser set it with boundary
    })
    .then(response => {
      // Handle 403 response
      if (response.status === 403) {
        setNotification({
          show: true,
          message: 'Access denied: You do not have permission to add messages to this ticket',
          type: 'error'
        });
        setSending(false);
        return null;
      }
      
      return response.json();
    })
    .then(data => {
      if (!data) return; // Handle 403 case
      
      if (data.success) {
        // Add message to state for immediate feedback
        const newMessage = {
          id: Date.now(), // Temporary ID
          type: 'customer',
          time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
          message: messageText || '',
          attachments: selectedFiles.map(file => ({
            file_name: file.name,
            file_size: file.size,
            file_type: file.type
          }))
        };
        
        setMessages(prevMessages => [...prevMessages, newMessage]);
        
        messageInputRef.current.value = '';
        clearFiles(); // Clear selected files
 
        // Refetch messages to get the accurate state from server
        setTimeout(fetchMessages, 500);
      } else {
        console.error("Failed to send message:", data.error);
        setNotification({
          show: true,
          message: data.message || "Failed to send message. Please try again.",
          type: 'error'
        });
      }
      setSending(false);
    })
    .catch(error => {
      console.error("Error sending message:", error);
      setNotification({
        show: true,
        message: "Network error. Please try again.",
        type: 'error'
      });
      setSending(false);
    });
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
    // Allow Shift+Enter for new lines in textarea
  };

  // State for confirmation dialog
  const [showConfirm, setShowConfirm] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmError, setConfirmError] = useState("");
  
  // Show confirmation dialog
  const promptCloseTicket = () => {
    setShowConfirm(true);
    setConfirmError("");
  };
  
  // Cancel confirmation
  const cancelCloseTicket = () => {
    setShowConfirm(false);
  };
  
  // Close ticket after confirmation
  const closeTicket = () => {
    setConfirmLoading(true);
    setConfirmError("");
    
    const token = getToken();
    
    if (!token) {
      setConfirmError('Authentication required. Please log in.');
      setConfirmLoading(false);
      return;
    }
    
    fetch("/api.php?f=close_ticket", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ticket_id: ticketId,
        token
      })
    })
    .then(response => {
      // Handle 403 response
      if (response.status === 403) {
        setConfirmError('Access denied: You do not have permission to close this ticket');
        setConfirmLoading(false);
        return null;
      }
      
      return response.json();
    })
    .then(data => {
      setConfirmLoading(false);
      if (!data) return; // Handle 403 case
      
      if (data.success) {
        setShowConfirm(false);
        fetchTicketDetails();
        fetchMessages();
      } else {
        setConfirmError("Failed to close ticket: " + (data.message || "Unknown error"));
      }
    })
    .catch(error => {
      console.error("Error closing ticket:", error);
      setConfirmLoading(false);
      setConfirmError("Network error. Please try again.");
    });
  };

  // File handling functions
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    const validFiles = [];
    let hasError = false;

    files.forEach(file => {
      if (file.size > maxSize) {
        setFileUploadError(`File "${file.name}" exceeds 5MB limit`);
        hasError = true;
      } else {
        validFiles.push(file);
      }
    });

    if (!hasError) {
      setFileUploadError('');
      setSelectedFiles(validFiles);
    }
  };

  const removeFile = (index) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    if (newFiles.length === 0) {
      fileInputRef.current.value = '';
    }
  };

  const clearFiles = () => {
    setSelectedFiles([]);
    setFileUploadError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Format file size for display
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Download attachment function
  const downloadAttachment = (attachmentId, fileName) => {
    const token = getToken();
    if (!token) {
      setNotification({
        show: true,
        message: 'Authentication required. Please log in.',
        type: 'error'
      });
      return;
    }

    // Open download URL in new window
    const downloadUrl = `/api.php?f=download_attachment&id=${attachmentId}&token=${encodeURIComponent(token)}`;
    window.open(downloadUrl, '_blank');
  };

  // Initial load
  useEffect(() => {
    if (ticketId) {
      fetchTicketDetails();
      fetchMessages();
      
      // Refresh messages every 30 seconds (only if access is granted)
      const interval = setInterval(() => {
        if (hasAccess === true) {
          fetchMessages();
        }
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [ticketId, hasAccess]);

  // Chat messages component
  const ChatBody = () => {
    if (loading && messages.length === 0) {
      return (
        <div className="d-flex justify-content-center align-items-center p-5">
          <div className="spinner-border text-primary" role="status">
          </div>
        </div>
      );
    }
    
    // Ensure messages is an array
    const messageList = Array.isArray(messages) ? messages : [];
    
    if (messageList.length === 0 && !loading) {
      return (
        <div className="text-center p-5 text-muted">
          <i className="fa fa-comment-o fa-3x mb-3"></i>
          <p>No messages yet</p>
        </div>
      );
    }
    
    return (
      <>
        {messageList.map((message, index) => (
          <div key={message.id || index} className={message.type === 'zet' ? "media mt-1" : "media flex-row-reverse"}>
            <div className="media-body">
              <div className="main-msg-wrapper" style={{
                maxWidth: '75%',
                wordWrap: 'break-word',
                wordBreak: 'break-word',
                overflowWrap: 'break-word',
                hyphens: 'auto'
              }}>
                {message.message && message.message.split('\n').map((line, i) => (
                  <React.Fragment key={i}>
                    {line}
                    {i !== message.message.split('\n').length - 1 && <br />}
                  </React.Fragment>
                ))}
                
                                {/* Display attachments */}
                {message.attachments && message.attachments.length > 0 && (
                  <div className="mt-3">
                    <div className="mb-2">
                      <small className="text-muted">
                        <i className="fa fa-paperclip mr-1"></i>
                        {message.attachments.length} attachment{message.attachments.length > 1 ? 's' : ''}
                      </small>
                    </div>
                    {message.attachments.map((attachment, attachIndex) => {
                      // Get file icon based on type
                      const getFileIcon = (fileType, fileName) => {
                        if (fileType?.includes('image/')) return 'fa-file-image-o';
                        if (fileType?.includes('pdf')) return 'fa-file-pdf-o';
                        if (fileType?.includes('word') || fileName?.endsWith('.doc') || fileName?.endsWith('.docx')) return 'fa-file-word-o';
                        if (fileType?.includes('excel') || fileName?.endsWith('.xls') || fileName?.endsWith('.xlsx')) return 'fa-file-excel-o';
                        if (fileType?.includes('zip') || fileType?.includes('rar')) return 'fa-file-archive-o';
                        if (fileType?.includes('text')) return 'fa-file-text-o';
                        return 'fa-file-o';
                      };

                      return (
                        <div key={attachIndex} className="attachment-item mb-2">
                          {attachment.id ? (
                            <button 
                              onClick={() => downloadAttachment(attachment.id, attachment.file_name)}
                              className="btn btn-light border w-100 text-left p-3 d-flex align-items-center"
                              style={{
                                borderRadius: '8px',
                                transition: 'all 0.2s ease',
                                borderColor: '#dee2e6'
                              }}
                              onMouseEnter={(e) => {
                                e.target.style.backgroundColor = '#f8f9fa';
                                e.target.style.borderColor = '#007bff';
                                e.target.style.transform = 'translateY(-1px)';
                                e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                              }}
                              onMouseLeave={(e) => {
                                e.target.style.backgroundColor = '#f8f9fa';
                                e.target.style.borderColor = '#dee2e6';
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = 'none';
                              }}
                            >
                              <div className="mr-3">
                                <i className={`fa ${getFileIcon(attachment.file_type, attachment.file_name)} fa-2x text-primary`}></i>
                              </div>
                              <div className="flex-grow-1">
                                <div className="font-weight-bold text-dark mb-1" style={{fontSize: '14px'}}>
                                  {attachment.file_name}
                                </div>
                                <div className="text-muted" style={{fontSize: '12px'}}>
                                  {attachment.file_size && formatFileSize(attachment.file_size)}
                                  {attachment.file_type && ` • ${attachment.file_type.split('/')[1]?.toUpperCase()}`}
                                </div>
                              </div>
                              <div className="ml-2">
                                <i className="fa fa-download text-primary"></i>
                              </div>
                            </button>
                          ) : (
                            <div className="btn btn-light border w-100 text-left p-3 d-flex align-items-center" style={{borderRadius: '8px'}}>
                              <div className="mr-3">
                                <i className={`fa ${getFileIcon(attachment.file_type, attachment.file_name)} fa-2x text-muted`}></i>
                              </div>
                              <div className="flex-grow-1">
                                <div className="font-weight-bold text-dark mb-1" style={{fontSize: '14px'}}>
                                  {attachment.file_name}
                                </div>
                                <div className="text-muted" style={{fontSize: '12px'}}>
                                  {attachment.file_size && formatFileSize(attachment.file_size)}
                                  {attachment.file_type && ` • ${attachment.file_type.split('/')[1]?.toUpperCase()}`}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
              <div>
                <span>{message.time}</span>
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </>
    );
  };

  // Show loading state
  if (hasAccess === null) {
    return (
      <>
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/support">Support</Link></li>
              <li className="breadcrumb-item1">Ticket #{ticketId}</li>
            </ol>
          </div>
        </div>
        
        <div className="row">
          <div className="col-12">
            <div className="card noheight">
              <div className="card-header">
                <div className="card-title">Help Desk</div>
              </div>
              <div className="card-body">
                <div className="d-flex justify-content-center p-5">
                  <div className="spinner-border text-primary" role="status">
               
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Show no access message
  if (hasAccess === false) {
    return (
      <>
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/support">Support</Link></li>
              <li className="breadcrumb-item1">Ticket #{ticketId}</li>
            </ol>
          </div>
        </div>
        
        <div className="row">
          <div className="col-12">
            <div className="card noheight">
              <div className="card-header">
                <div className="card-title">Help Desk</div>
              </div>
              <div className="card-body">
                <NoAccessMessage />
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/support">Support</Link></li>
            <li className="breadcrumb-item1">Ticket #{ticketId}</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>
            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card noheight">
            <div className="card-header">
              <div className="card-title">Help Desk</div>
              <div className="card-options">
                {ticket && ticket.status !== 'Closed' && (
                  <button onClick={promptCloseTicket} className="btn btn-danger btn-sm mr-2">
                    Close Ticket
                  </button>
                )}
              </div>
            </div>
            <div className="row no-gutters">
              <div className="col-xl-12 col-lg-12">
                <div className="border-left">
                  <div className="main-content-body main-content-body-chat">
                    <div className="main-chat-header p-3">
                      <div className="main-chat-msg-name">
       
                        {ticket && (
                          <>
                            <div className="d-flex align-items-center mb-1">
                              <h4 className="mb-0">{ticket.subject}</h4>
                                          <span className={`badge ml-2 text-white bg-${
              ticket.status === 'Open' ? 'primary' : 
              ticket.status === 'Customer Reply' ? 'info' :
              ticket.status === 'In Progress' ? 'warning' :
              ticket.status === 'Answered' ? 'success' :
              ticket.status === 'Closed' ? 'danger' : 'secondary'
            }`}>
              {ticket.status}
            </span>
                            </div>
                            <small>Department: {ticket.department} | Priority: {ticket.priority || 'Low'}</small><br />
                            <small>Created: {ticket.created_date} | Last update: {ticket.last_reply_date}</small>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="main-chat-body" id="ChatBody">
                      <div className="content-inner">
                        <ChatBody />
                      </div>
                    </div>
                    <div className="main-chat-footer" style={{
                      display: 'flex', 
                      flexDirection: 'column', 
                      padding: '0',
                      minHeight: 'auto',
                      height: 'auto',
                      overflow: 'visible'
                    }}>
                      {/* File attachment preview */}
                      {selectedFiles.length > 0 && (
                        <div className="attachment-preview p-3 border-top bg-light">
                          <div className="d-flex justify-content-between align-items-center mb-3">
                            <div className="text-muted">
                              <i className="fa fa-paperclip mr-2"></i>
                              <strong>{selectedFiles.length}</strong> file{selectedFiles.length > 1 ? 's' : ''} ready to upload
                            </div>
                            <button 
                              type="button" 
                              className="btn btn-sm btn-outline-danger"
                              onClick={clearFiles}
                            >
                              <i className="fa fa-times mr-1"></i>
                              Clear all
                            </button>
                          </div>
                          {selectedFiles.map((file, index) => {
                            // Get file icon based on type
                            const getFileIcon = (file) => {
                              if (file.type.includes('image/')) return 'fa-file-image-o';
                              if (file.type.includes('pdf')) return 'fa-file-pdf-o';
                              if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) return 'fa-file-word-o';
                              if (file.type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) return 'fa-file-excel-o';
                              if (file.type.includes('zip') || file.type.includes('rar')) return 'fa-file-archive-o';
                              if (file.type.includes('text')) return 'fa-file-text-o';
                              return 'fa-file-o';
                            };

                            return (
                              <div key={index} className="d-flex align-items-center p-2 mb-2 bg-white border rounded shadow-sm">
                                <div className="mr-3">
                                  <i className={`fa ${getFileIcon(file)} fa-lg text-primary`}></i>
                                </div>
                                <div className="flex-grow-1">
                                  <div className="font-weight-bold text-dark" style={{fontSize: '13px'}}>
                                    {file.name}
                                  </div>
                                  <div className="text-muted" style={{fontSize: '11px'}}>
                                    {formatFileSize(file.size)}
                                    {file.type && ` • ${file.type.split('/')[1]?.toUpperCase()}`}
                                  </div>
                                </div>
                                <button 
                                  type="button" 
                                  className="btn btn-sm btn-outline-danger ml-2"
                                  onClick={() => removeFile(index)}
                                  style={{padding: '4px 8px'}}
                                >
                                  <i className="fa fa-times"></i>
                                </button>
                              </div>
                            );
                          })}
                        </div>
                      )}
                      
                      {/* File upload error */}
                      {fileUploadError && (
                        <div className="alert alert-danger alert-sm py-1 px-2 mx-2 mb-2">
                          <small>{fileUploadError}</small>
                        </div>
                      )}
                      
                      {/* Input area with full width and auto-resize */}
                      <div style={{
                        display: 'flex', 
                        alignItems: 'flex-end', 
                        gap: '8px', 
                        padding: '15px', 
                        width: '100%', 
                        boxSizing: 'border-box',
                        minHeight: 'auto'
                      }}>
                        {/* Message textarea - takes up most of the width */}
                        <textarea 
                          className="form-control" 
                          placeholder="Type your message here..." 
                          ref={messageInputRef}
                          onKeyPress={handleKeyPress}
                          onInput={(e) => {
                            // Auto-resize textarea based on content
                            e.target.style.height = 'auto';
                            e.target.style.height = Math.min(e.target.scrollHeight, 120) + 'px';
                          }}
                          disabled={sending || (ticket && ticket.status === 'Closed')}
                          rows="1"
                          style={{
                            resize: 'none',
                            minHeight: '40px',
                            maxHeight: '120px',
                            flex: '1',
                            border: '1px solid #ced4da',
                            borderRadius: '0.375rem',
                            lineHeight: '1.5',
                            overflow: 'hidden'
                          }}
                        />
                        
                        {/* File input (hidden) */}
                        <input
                          type="file"
                          ref={fileInputRef}
                          onChange={handleFileSelect}
                          multiple
                          className="d-none"
                          disabled={sending || (ticket && ticket.status === 'Closed')}
                        />
                        
                        {/* Button group on the right */}
                        <div style={{display: 'flex', flexDirection: 'column', gap: '4px', flexShrink: 0}}>
                          {/* Attach file button */}
                          <button
                            type="button"
                            className="btn btn-outline-secondary"
                            onClick={() => fileInputRef.current?.click()}
                            disabled={sending || (ticket && ticket.status === 'Closed')}
                            title="Attach files (max 5MB each)"
                            style={{
                              width: '44px',
                              height: '34px',
                              padding: '0',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                          >
                            <i className="fa fa-paperclip"></i>
                          </button>
                          
                          {/* Send button */}
                          <button
                            type="button"
                            className="btn btn-primary"
                            onClick={sendMessage}
                            disabled={sending || (ticket && ticket.status === 'Closed')}
                            title="Send message"
                            style={{
                              width: '44px',
                              height: '34px',
                              padding: '0',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                          >
                            {sending ? (
                              <div className="spinner-border spinner-border-sm text-white" role="status" style={{width: '16px', height: '16px'}}>
                                <span className="sr-only">Sending...</span>
                              </div>
                            ) : (
                              <i className="fa fa-paper-plane"></i>
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Confirmation Modal */}
      {showConfirm && (
        <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Confirm Close Ticket</h5>
                <button type="button" className="close" onClick={cancelCloseTicket}>
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to close this ticket? This action cannot be undone.</p>
                {confirmError && (
                  <div className="alert alert-danger mt-3">
                    {confirmError}
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary" 
                  onClick={cancelCloseTicket}
                  disabled={confirmLoading}
                >
                  Cancel
                </button>
                <button 
                  type="button" 
                  className="btn btn-danger" 
                  onClick={closeTicket}
                  disabled={confirmLoading}
                >
                  {confirmLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span>
                      Closing...
                    </>
                  ) : 'Close Ticket'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notification Toast */}
      {notification.show && (
        <div 
          className={`toast show position-fixed`} 
          style={{top: '20px', right: '20px', zIndex: 9999}}
          role="alert" 
          aria-live="assertive" 
          aria-atomic="true"
        >
          <div className={`toast-header bg-${notification.type === 'error' ? 'danger' : 'success'} text-white`}>
            <strong className="mr-auto">
              {notification.type === 'error' ? 'Error' : 'Success'}
            </strong>
            <button 
              type="button" 
              className="ml-2 mb-1 close text-white" 
              onClick={() => setNotification({...notification, show: false})}
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div className="toast-body">
            {notification.message}
          </div>
        </div>
      )}
    </>
  );
};

export default Ticket;