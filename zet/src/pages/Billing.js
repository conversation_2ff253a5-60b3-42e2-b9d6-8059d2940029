import React, { useEffect, useState, useRef } from "react";
import { Outlet, Link, useParams, useLocation, useNavigate } from "react-router-dom";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Toast } from 'primereact/toast';
import 'primereact/resources/themes/mdc-light-indigo/theme.css';
import "primereact/resources/primereact.min.css";    
import 'primeflex/primeflex.css';
import DiscountAlert from "../components/DiscountAlert";

const Billing = () => {
  let navigate = useNavigate(); 
  const routeChange = (path) =>{ 
    navigate(path);
  }
  const toast = useRef(null);
  const path = useLocation();
  const params = useParams();
  const [bills, setBill] = useState([]);
  const [history, setHistory] = useState([]);
  const [cards, setCard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [creditLoading, setCreditLoading] = useState(true);
  const [billsLoading, setBillsLoading] = useState(true);
  const [historyLoading, setHistoryLoading] = useState(true);
  const [cardsLoading, setCardsLoading] = useState(true);
  const [payingInvoice, setPayingInvoice] = useState(false);
  



  // Add access control state
  const [creditAccess, setCreditAccess] = useState(null); // null = loading, true = access, false = denied
  const [invoicesAccess, setInvoicesAccess] = useState(null);
  const [billingAccess, setBillingAccess] = useState(null); // For history and payment methods
  const [creditAccessError, setCreditAccessError] = useState('');
  const [invoicesAccessError, setInvoicesAccessError] = useState('');
  const [billingAccessError, setBillingAccessError] = useState('');


  const [stripe, setStripe] = useState(null);

  const [elements, setElements] = useState(null);
  const cardElementRef = useRef(null);
  
  
  const [creditStats, setCreditStats] = useState({
    available_credit: 0,
    free_credit: 0,
    pending_credit: 0
  });
  const [billingStats, setBillingStats] = useState({
    monthly_spending: 0,
    yearly_spending: 0,
    last_payment: 0,
    active_services: 0,
    unpaid_amount: 0
  });
  const [paymentMethod, setPaymentMethod] = useState('cc');
  const [creditAmount, setCreditAmount] = useState('');
  const [addingCredit, setAddingCredit] = useState(false);
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [showAddPayPalModal, setShowAddPayPalModal] = useState(false);

  const [newCard, setNewCard] = useState({
    cardNumber: '',
    cardName: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: ''
  });
  const [addingCard, setAddingCard] = useState(false);
  const [addingPayPal, setAddingPayPal] = useState(false);

  const [paymentMethodType, setPaymentMethodType] = useState('stripe'); // 'stripe' or 'paypal'

  // Function to get token from session storage
  function getToken() {
    return sessionStorage.getItem('token');
  }

  // No Access Component
  const NoAccessMessage = ({ message, section }) => (
    <div className="text-center p-5">
      <i className="fa fa-lock fa-4x text-danger mb-4"></i>
      <h3 className="text-danger mb-3">Access Denied</h3>
      <p className="text-muted mb-4" style={{fontSize: '1.1rem'}}>
        {message || `You do not have permission to access ${section}.`}
      </p>
      <p className="text-muted">
        Please contact your administrator if you believe this is an error.
      </p>
      <div className="mt-4">
        <button 
          className="btn btn-secondary me-2" 
          onClick={() => window.location.reload()}
        >
          <i className="fa fa-refresh me-1"></i> Refresh Page
        </button>
      </div>
    </div>
  );
  
  // Function to fetch billing data (invoices)
  const fetchBillingData = () => {
    setBillsLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=bills", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        console.log("Bills Response status:", response.status);
        
        // Check for 403 status code BEFORE trying to parse JSON
        if (response.status === 403) {
          console.log("403 detected - setting no access for invoices");
          setInvoicesAccess(false);
          setInvoicesAccessError('You do not have permission to access invoices');
          setBillsLoading(false);
          throw new Error('Access denied'); // Stop the promise chain
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("Bills Response Data:", data);
        
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
            window.location.reload(false);
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            setInvoicesAccess(false);
            setInvoicesAccessError(data.message || 'Access denied');
            setBillsLoading(false);
            return;
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error fetching invoices: ${data.error}`,
              life: 3000
            });
          }
        } else {
          setBill(Array.isArray(data) ? data : []);
          setInvoicesAccess(true);
        }
        setBillsLoading(false);
      })
      .catch(error => {
        console.error("Error fetching billing data:", error);
        if (error.message !== 'Access denied') {
          setInvoicesAccess(true); // Keep accessible for network errors
        }
        setBillsLoading(false);
      });
  }

  const toggleAddCardModal = () => {
    setShowAddCardModal(!showAddCardModal);
    // Reset form when opening
    if (!showAddCardModal) {
      setNewCard({
        cardNumber: '',
        cardName: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: ''
      });
    }
  };



  const handleAddCardSecure = async (e) => {
    e.preventDefault();
    
    if (!stripe || !elements) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Payment system not ready. Please try again.',
        life: 3000
      });
      return;
    }
    
    if (!newCard.cardName.trim()) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Please enter the cardholder name',
        life: 3000
      });
      return;
    }
    
    setAddingCard(true);
    
    try {
      const token = getToken();
      
      // Step 1: Create Setup Intent on backend
      const setupResponse = await fetch("/api.php?f=add_stripe_payment_method", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });
      
      if (setupResponse.status === 403) {
        toast.current.show({
          severity: 'error',
          summary: 'Access Denied',
          detail: 'You do not have permission to add payment methods',
          life: 3000
        });
        setAddingCard(false);
        return;
      }
      
      const setupData = await setupResponse.json();
      
      if (setupData.error) {
        throw new Error(setupData.error);
      }
      
      if (!setupData.setup_intent || !setupData.setup_intent.client_secret) {
        throw new Error('Failed to create setup intent');
      }
      
      // Get the card element
      const cardElement = elements.getElement('card');
      
      // Step 2: Confirm Setup Intent with Stripe.js
      const { error, setupIntent } = await stripe.confirmCardSetup(
        setupData.setup_intent.client_secret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: newCard.cardName
            }
          }
        }
      );
      
      if (error) {
        throw new Error(error.message || 'Card verification failed');
      }
      
      // Step 3: Save the confirmed payment method on backend
      const saveResponse = await fetch("/api.php?f=add_stripe_payment_method", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          setup_intent_id: setupIntent.id
        })
      });
      
      const saveData = await saveResponse.json();
      
      if (saveData.error) {
        throw new Error(saveData.error);
      }
      
      // Success - refresh payment methods list
      fetchCardData();
      
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Payment method added successfully and ready for automatic payments',
        life: 3000
      });
      
      // Reset form and close modal
      setNewCard({
        cardNumber: '',
        cardName: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: ''
      });
      setShowAddCardModal(false);
      
    } catch (error) {
      console.error('Error adding payment method:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.message || 'Failed to add payment method',
        life: 5000
      });
    } finally {
      setAddingCard(false);
    }
  };



  // Add this function to handle input changes
  const handleCardInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'cardNumber') {
      // Format card number with spaces
      const cleanValue = value.replace(/\D/g, '');
      let formattedValue = '';
      
      for (let i = 0; i < cleanValue.length; i++) {
        if (i > 0 && i % 4 === 0) {
          formattedValue += ' ';
        }
        formattedValue += cleanValue[i];
      }
      
      setNewCard(prev => ({
        ...prev,
        [name]: formattedValue
      }));
    } else {
      setNewCard(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Function to fetch credit data
  const fetchCreditData = () => {
    setCreditLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=credit", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        console.log("Credit Response status:", response.status);
        
        // Check for 403 status code BEFORE trying to parse JSON
        if (response.status === 403) {
          console.log("403 detected - setting no access for credit");
          setCreditAccess(false);
          setCreditAccessError('You do not have permission to access credit information');
          setCreditLoading(false);
          throw new Error('Access denied');
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("Credit Response Data:", data);
        
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
            window.location.reload(false);
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            setCreditAccess(false);
            setCreditAccessError(data.message || 'Access denied');
            setCreditLoading(false);
            return;
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error fetching credit info: ${data.error}`,
              life: 3000
            });
          }
        } else {
          setCreditStats(data);
          setCreditAccess(true);
        }
        setCreditLoading(false);
      })
      .catch(error => {
        console.error('Error fetching credit info:', error);
        if (error.message !== 'Access denied') {
          setCreditAccess(true); // Keep accessible for network errors
          toast.current.show({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to fetch credit information. Please try again.',
            life: 3000
          });
        }
        setCreditLoading(false);
      });
  }

  // Function to handle adding Stripe payment method
  const handleAddCard = async (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!newCard.cardNumber.trim() || !newCard.cardName.trim() || 
        !newCard.expiryMonth || !newCard.expiryYear || !newCard.cvv.trim()) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill in all required fields',
        life: 3000
      });
      return;
    }
    
    setAddingCard(true);
    
    try {
      const token = getToken();
      
      // Step 1: Create Setup Intent on backend
      const setupResponse = await fetch("/api.php?f=add_stripe_payment_method", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token
        })
      });
      
      if (setupResponse.status === 403) {
        toast.current.show({
          severity: 'error',
          summary: 'Access Denied',
          detail: 'You do not have permission to add payment methods',
          life: 3000
        });
        setAddingCard(false);
        return;
      }
      
      const setupData = await setupResponse.json();
      
      if (setupData.error) {
        throw new Error(setupData.error);
      }
      
      if (!setupData.setup_intent || !setupData.setup_intent.client_secret) {
        throw new Error('Failed to create setup intent');
      }
      
      // Check if Stripe.js is loaded
      if (!stripe) {
        throw new Error('Stripe.js not loaded. Please refresh the page and try again.');
      }
      
      // Step 2: Confirm Setup Intent with Stripe.js
      const cardNumberClean = newCard.cardNumber.replace(/\s+/g, '');
      
      // Create card element data
      const { error, setupIntent } = await stripe.confirmCardSetup(
        setupData.setup_intent.client_secret,
        {
          payment_method: {
            card: {
              number: cardNumberClean,
              exp_month: parseInt(newCard.expiryMonth),
              exp_year: parseInt(newCard.expiryYear),
              cvc: newCard.cvv
            },
            billing_details: {
              name: newCard.cardName
            }
          }
        }
      );
      
      if (error) {
        // Show error to customer
        throw new Error(error.message || 'Card verification failed');
      }
      
      // Step 3: Save the confirmed payment method on backend
      const saveResponse = await fetch("/api.php?f=add_stripe_payment_method", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          setup_intent_id: setupIntent.id
        })
      });
      
      const saveData = await saveResponse.json();
      
      if (saveData.error) {
        throw new Error(saveData.error);
      }
      
      // Success - refresh payment methods list
      fetchCardData();
      
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Payment method added successfully and ready for automatic payments',
        life: 3000
      });
      
      setShowAddCardModal(false);
      
    } catch (error) {
      console.error('Error adding payment method:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.message || 'Failed to add payment method',
        life: 5000
      });
    } finally {
      setAddingCard(false);
    }
  };
  
  const handleAddCardWithElements = async (e) => {
    e.preventDefault();
    
    if (!stripe || !elements) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Stripe not properly initialized',
        life: 3000
      });
      return;
    }
    
    setAddingCard(true);
    
    try {
      const token = getToken();
      
      // Step 1: Create Setup Intent
      const setupResponse = await fetch("/api.php?f=add_stripe_payment_method", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });
      
      const setupData = await setupResponse.json();
      
      if (setupData.error) {
        throw new Error(setupData.error);
      }
      
      // Step 2: Confirm with Stripe Elements
      const { error, setupIntent } = await stripe.confirmCardSetup(
        setupData.setup_intent.client_secret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: newCard.cardName
            }
          }
        }
      );
      
      if (error) {
        throw new Error(error.message);
      }
      
      // Step 3: Save on backend
      const saveResponse = await fetch("/api.php?f=add_stripe_payment_method", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          setup_intent_id: setupIntent.id
        })
      });
      
      const saveData = await saveResponse.json();
      
      if (saveData.error) {
        throw new Error(saveData.error);
      }
      
      // Success
      fetchCardData();
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Payment method added successfully',
        life: 3000
      });
      setShowAddCardModal(false);
      
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.message || 'Failed to add payment method',
        life: 5000
      });
    } finally {
      setAddingCard(false);
    }
  };

  // Function to handle adding PayPal payment method
  const handleAddPayPal = async (e) => {
    e.preventDefault();
    setAddingPayPal(true);
    
    try {
      const token = getToken();
      
      // Create PayPal payment method for automatic payments
      const response = await fetch("/api.php?f=add_paypal_payment_method", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          processor: 'paypal',
          return_url: `${window.location.origin}/billing/methods?paypal_return=1`,
          cancel_url: `${window.location.origin}/billing/methods?paypal_cancel=1`
        })
      });
      
      if (response.status === 403) {
        toast.current.show({
          severity: 'error',
          summary: 'Access Denied',
          detail: 'You do not have permission to add payment methods',
          life: 3000
        });
        setAddingPayPal(false);
        return;
      }
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.message || data.error);
      } else if (data.approval_url) {
        // Redirect to PayPal for approval
        window.location.href = data.approval_url;
      } else {
        // If no redirect needed, refresh the list
        fetchCardData();
        
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'PayPal payment method added successfully',
          life: 3000
        });
        
        setShowAddPayPalModal(false);
      }
    } catch (error) {
      console.error('Error adding PayPal payment method:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to add PayPal payment method: ${error.message}`,
        life: 5000
      });
    } finally {
      setAddingPayPal(false);
    }
  };


  

  // Function to fetch billing statistics
  const fetchBillingStats = () => {
    setLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=billing_stats", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        // Handle 403 response
        if (response.status === 403) {
          console.log("403 detected for billing stats - user may not have billing access");
          // Don't block the whole page for billing stats failure
          setLoading(false);
          return null;
        }
        
        return response.json();
      })
      .then(data => {
        if (!data) return; // Handle 403 case
        
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
            window.location.reload(false);
          } 
        } else {
          setBillingStats(data);
        }
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching billing stats:', error);
        setLoading(false);
      });
  }
  
  // Function to fetch payment history
  const fetchHistoryData = () => {
    setHistoryLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=history", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        console.log("History Response status:", response.status);
        
        // Check for 403 status code BEFORE trying to parse JSON
        if (response.status === 403) {
          console.log("403 detected - setting no access for payment history");
          setBillingAccess(false);
          setBillingAccessError('You do not have permission to access payment history');
          setHistoryLoading(false);
          throw new Error('Access denied');
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("History Response Data:", data);
        
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
            window.location.reload(false);
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            setBillingAccess(false);
            setBillingAccessError(data.message || 'Access denied');
            setHistoryLoading(false);
            return;
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error fetching payment history: ${data.error}`,
              life: 3000
            });
          }
        } else {
          // Ensure data is always treated as an array
          setHistory(Array.isArray(data) ? data : []);
          setBillingAccess(true);
        }
        setHistoryLoading(false);
      })
      .catch(error => {
        console.error("Error fetching history data:", error);
        if (error.message !== 'Access denied') {
          setBillingAccess(true); // Keep accessible for network errors
        }
        setHistoryLoading(false);
      });
  }
  
  // Function to fetch saved payment methods for automatic payments
  const fetchCardData = () => {
    setCardsLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=get_payment_methods", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        token
      })
    })
      .then(response => {
        console.log("Payment Methods Response status:", response.status);
        
        if (response.status === 403) {
          console.log("403 detected - setting no access for payment methods");
          setBillingAccess(false);
          setBillingAccessError('You do not have permission to access payment methods');
          setCardsLoading(false);
          throw new Error('Access denied');
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("Payment Methods Response Data:", data);
        
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
            window.location.reload(false);
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            setBillingAccess(false);
            setBillingAccessError(data.message || 'Access denied');
            setCardsLoading(false);
            return;
          } else if (data.error === 'No payment methods found') {
            setCard([]);
            setBillingAccess(true);
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error fetching payment methods: ${data.error}`,
              life: 3000
            });
          }
        } else {
          setCard(Array.isArray(data) ? data : []);
          setBillingAccess(true);
        }
        setCardsLoading(false);
      })
      .catch(error => {
        console.error("Error fetching payment methods:", error);
        if (error.message !== 'Access denied') {
          setBillingAccess(true);
        }
        setCardsLoading(false);
      });
  }

  // Function to add credit
  const handleAddCredit = () => {
    const numericCreditAmount = parseFloat(creditAmount);
    
    if (isNaN(numericCreditAmount) || numericCreditAmount < 300) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Minimum amount is €300',
        life: 3000
      });
      return;
    }
    
    setAddingCredit(true);
    
    const token = sessionStorage.getItem('token');
    
    fetch("/api.php?f=generate_credit_invoice", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        payment_method: paymentMethod,
        amount: numericCreditAmount
      })
    })
    .then(response => {
      // Handle 403 response
      if (response.status === 403) {
        toast.current.show({
          severity: 'error',
          summary: 'Access Denied',
          detail: 'You do not have permission to generate credit invoices',
          life: 3000
        });
        setAddingCredit(false);
        return null;
      }
      
      // Important: Parse JSON and check for errors
      return response.json().then(data => {
        if (!response.ok) {
          throw new Error(data.details || 'Unknown error occurred');
        }
        return data;
      });
    })
    .then(data => {
      if (!data) return; // Handle 403 case
      
      if (data.error) {
        throw new Error(data.details || data.error);
      }
      
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Credit purchase invoice generated successfully!',
        life: 3000
      });
      
      // Navigate to invoice or refresh credit data
      if (data.invoice_id) {
        navigate(`/billing/invoices/${data.invoice_id}`);
      }
      
      // Optional: Show bonus information
      if (data.bonus) {
        toast.current.show({
          severity: 'info',
          summary: 'Bonus Credited',
          detail: `You received a bonus of €${data.bonus}!`,
          life: 3000
        });
      }
    })
    .catch(error => {
      console.error('Credit Invoice Error:', error);
      
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.message || 'Failed to generate credit invoice',
        life: 3000
      });
    })
    .finally(() => {
      setAddingCredit(false);
    });
  };

  useEffect(() => {
    // Check if Stripe.js is already loaded
    if (window.Stripe) {
      const stripeInstance = window.Stripe('pk_test_51RR8eZQR2z1KpIslDLYwlfIHnPV7HIuAeZ7PejZt1RQBv7LTv0uzte8ssW0Vpz13GvAg0MLDCu2bUbiSeHl3sMR700iIBPxYxP');
      setStripe(stripeInstance);
    } else {
      // Load Stripe.js dynamically
      const script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/';
      script.async = true;
      script.onload = () => {
        const stripeInstance = window.Stripe('pk_test_51RR8eZQR2z1KpIslDLYwlfIHnPV7HIuAeZ7PejZt1RQBv7LTv0uzte8ssW0Vpz13GvAg0MLDCu2bUbiSeHl3sMR700iIBPxYxP');
        setStripe(stripeInstance);
      };
      document.body.appendChild(script);
    }
  }, []);

  useEffect(() => {
    if (stripe && showAddCardModal) {
      const elementsInstance = stripe.elements();
      setElements(elementsInstance);
      
      // Create card element with custom styling
      const cardElement = elementsInstance.create('card', {
        style: {
          base: {
            fontSize: '16px',
            color: '#495057',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
            '::placeholder': {
              color: '#6c757d',
            },
          },
          invalid: {
            color: '#dc3545',
            iconColor: '#dc3545',
          },
        },
      });
      
      // Mount card element when container is ready
      setTimeout(() => {
        if (cardElementRef.current) {
          cardElement.mount(cardElementRef.current);
        }
      }, 100);
      
      // Cleanup on unmount
      return () => {
        cardElement.unmount();
      };
    }
  }, [stripe, showAddCardModal]);
  
  // Initial data fetch
  useEffect(() => {
    fetchBillingData();
    fetchHistoryData();
    fetchCardData();
    fetchCreditData();
    fetchBillingStats();
    
    // Handle PayPal and other payment method callbacks
    const urlParams = new URLSearchParams(window.location.search);
    const paypalSuccess = urlParams.get('paypal_success');
    const paypalReturn = urlParams.get('paypal_return');
    const paypalCancel = urlParams.get('paypal_cancel');

    const paypalToken = urlParams.get('token') || urlParams.get('setup_token') || urlParams.get('approval_token_id');
    
    // Enhanced debugging for all parameters
    console.log('Payment callback parameters:', {
      paypalSuccess,
      paypalReturn,
      paypalCancel,

      paypalToken,
      allParams: Object.fromEntries(urlParams.entries())
    });
    
    if ((paypalSuccess === '1' || paypalReturn === '1') && paypalToken) {
      // Confirm PayPal vaulting
      const token = getToken();
      
      fetch('/api.php?f=confirm_paypal_vaulting', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: paypalToken,
          user_token: token
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'PayPal payment method vaulted successfully!',
            life: 3000
          });
          fetchCardData(); // Refresh payment methods
        } else {
          toast.current.show({
            severity: 'error',
            summary: 'Error',
            detail: data.error || 'Failed to vault PayPal payment method',
            life: 5000
          });
        }
        
        // Clean up URL parameters
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      })
      .catch(error => {
        console.error('Error vaulting PayPal payment method:', error);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to vault PayPal payment method',
          life: 5000
        });
        
        // Clean up URL parameters
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      });
    } else if (paypalCancel === '1') {
      toast.current.show({
        severity: 'warn',
        summary: 'Cancelled',
        detail: 'PayPal payment method setup was cancelled',
        life: 3000
      });
      
      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    } 
  }, []);
  

  
  // Re-fetch billing data when path changes
  useEffect(() => {
    const pathPart = splitLastOccurrence(path.pathname, '/');
    
    if(pathPart === 'invoices' || path.pathname === '/billing') {
      fetchBillingData();
    }
    if(pathPart === 'history') {
      fetchHistoryData();
    }
    if(pathPart === 'methods') {
      fetchCardData();
    }
    if(pathPart === 'credit' || path.pathname === '/billing') {
      fetchCreditData();
    }
  }, [path.pathname]);

  // Helper function to get the last part of the URL path
  function splitLastOccurrence(str, substring) {
    const lastIndex = str.lastIndexOf(substring);
    const before = str.slice(0, lastIndex);
    const after = str.slice(lastIndex + 1);
    return after;
  }

  // Add these validation functions
  const validateCardNumber = (number) => {
    // Remove spaces and non-digit characters
    const cleanNumber = number.replace(/\D/g, '');
    // Check if the number has valid length
    return cleanNumber.length >= 13 && cleanNumber.length <= 19;
  };

  const validateExpiryDate = (month, year) => {
    if (!month || !year) return false;
    
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    
    const expiryMonth = parseInt(month, 10);
    const expiryYear = parseInt(year, 10);
    
    // Check if date is in the past
    if (expiryYear < currentYear || (expiryYear === currentYear && expiryMonth < currentMonth)) {
      return false;
    }
    
    return expiryMonth >= 1 && expiryMonth <= 12;
  };

  // Custom formatter for card types
  const cardType = (cards) => {
    let iconClass = "fa fa-credit-card"; // Default icon
    
    switch(cards.type.toLowerCase()) {
      case 'visa':
        iconClass = "fa fa-cc-visa";
        break;
      case 'mastercard':
        iconClass = "fa fa-cc-mastercard";
        break;
      case 'amex':
        iconClass = "fa fa-cc-amex";
        break;
      case 'discover':
        iconClass = "fa fa-cc-discover";
        break;
      default:
        iconClass = "fa fa-credit-card";
    }
    
    return (
      <div>
        <i style={{marginBottom:"-3px"}} className={iconClass}></i> &nbsp;{cards.type}
      </div>
    );
  };
  
  // Custom formatter for invoice status
  const statusFormatter = (rowData) => {
    if (rowData.status === 'Paid') {
      return <span className="badge bg-success">Paid</span>;
    } else if (rowData.status === 'Unpaid') {
      return <span className="badge bg-danger">Unpaid</span>;
    } else if (rowData.status === 'Overdue') {
      return <span className="badge bg-warning">Overdue</span>;
    } else {
      return <span className="badge bg-secondary">{rowData.status}</span>;
    }
  };
  
  // Custom formatter for amount with Euro symbol
  const amountFormatter = (rowData) => {
    return <span className="fw-bold">€{rowData.total}</span>;
  };
  
  // Function to handle paying all invoices
  const handlePayAllInvoices = () => {
    // This would need to be implemented based on your payment flow
    toast.current.show({
      severity: 'info',
      summary: 'Pay Invoices',
      detail: 'Payment processing would be implemented here',
      life: 3000
    });
  };
  
  const handlePayInvoice = (invoiceId) => {
    setPayingInvoice(true);
    const token = getToken();
    
    fetch("/api.php?f=process_paid_invoice", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        token,
        invoice_id: invoiceId
      })
    })
      .then(response => {
        return response.json();
      })
      .then(data => {
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
            window.location.reload(false);
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: data.error,
              life: 3000
            });
          }
        } else {
          toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Invoice paid successfully! Credit has been added to your account.',
            life: 3000
          });
          
          // Refresh data
          fetchCreditData();
          
          // If this is a credit purchase invoice, navigate back to credit tab
          if (data.invoice_type === 'Credit Purchase') {
            navigate('/billing/credit');
          }
        }
        setPayingInvoice(false);
      })
      .catch(error => {
        console.error('Error paying invoice:', error);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to process payment. Please try again.',
          life: 3000
        });
        setPayingInvoice(false);
      });
  };
  



  // Add a Pay button component for credit purchase invoices
  const CreditPurchaseInvoicePayButton = ({ invoice }) => {
    if (invoice.status === 'Paid' || invoice.type !== 'Credit Purchase') {
      return null;
    }
    
    return (
      <button 
        className="btn btn-success"
        onClick={() => handlePayInvoice(invoice.id)}
        disabled={payingInvoice}
      >
        {payingInvoice ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Processing Payment
          </>
        ) : (
          <>
            <i className="fa fa-credit-card me-1"></i> Pay Now & Add Credit
          </>
        )}
      </button>
    );
  };

  const CreditPurchaseNote = ({ invoice }) => {
    if (invoice.type !== 'Credit Purchase') {
      return null;
    }
    
    if (invoice.status === 'Paid') {
      return (
        <div className="alert alert-success" role="alert">
          <i className="fa fa-check-circle me-2"></i>
          <strong>Credit Added!</strong> Your account has been credited with €{invoice.total}.
          {invoice.bonus > 0 && (
            <span> Plus a bonus of €{invoice.bonus}!</span>
          )}
        </div>
      );
    }
    
    return (
      <div className="alert alert-info" role="alert">
        <i className="fa fa-info-circle me-2"></i>
        <strong>Credit Purchase:</strong> Once this invoice is paid, €{invoice.total} will be added to your account balance.
        {parseFloat(invoice.total) >= 500 && (
          <span> Plus you'll receive a 5% bonus credit!</span>
        )}
      </div>
    );
  }

  // Function to remove a payment method
  const handleRemovePaymentMethod = (id) => {
    if (!window.confirm("Are you sure you want to remove this payment method?")) {
      return;
    }
    
    setCardsLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=delete_payment_method", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        token,
        payment_method_id: id
      })
    })
      .then(response => {
        if (response.status === 403) {
          toast.current.show({
            severity: 'error',
            summary: 'Access Denied',
            detail: 'You do not have permission to delete payment methods',
            life: 3000
          });
          setCardsLoading(false);
          return null;
        }
        return response.json();
      })
      .then(data => {
        if (!data) return;
        
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
            window.location.reload(false);
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error removing payment method: ${data.message || data.error}`,
              life: 3000
            });
          }
        } else {
          // Refresh payment methods list
          fetchCardData();
          
          toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Payment method removed successfully',
            life: 3000
          });
        }
        setCardsLoading(false);
      })
      .catch(error => {
        console.error('Error removing payment method:', error);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to remove payment method. Please try again.',
          life: 3000
        });
        setCardsLoading(false);
      });
  };

  // Function to set default payment method
  const handleSetDefaultPaymentMethod = (id) => {
    const token = getToken();
    
    fetch("/api.php?f=set_default_payment_method", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        token,
        payment_method_id: id
      })
    })
      .then(response => {
        if (response.status === 403) {
          toast.current.show({
            severity: 'error',
            summary: 'Access Denied',
            detail: 'You do not have permission to modify payment methods',
            life: 3000
          });
          return null;
        }
        return response.json();
      })
      .then(data => {
        if (!data) return;
        
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
            window.location.reload(false);
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error setting default payment method: ${data.message || data.error}`,
              life: 3000
            });
          }
        } else {
          // Refresh payment methods list
          fetchCardData();
          
          toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Default payment method updated successfully',
            life: 3000
          });
        }
      })
      .catch(error => {
        console.error('Error setting default payment method:', error);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to set default payment method. Please try again.',
          life: 3000
        });
      });
  };

  return (
    <>
      <Toast ref={toast} />
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">Billing</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>
            </div>
          </div>
        </div>
      </div>
      
      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          {/* Add Stripe Card Modal */}
          {showAddCardModal && (
  <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
    <div className="modal-dialog" role="document">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">
            <i className="fa fa-cc-stripe me-2" style={{paddingRight: '0.5rem'}}></i>Add Stripe Payment Method
          </h5>
          <button type="button" className="close" onClick={toggleAddCardModal}>
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <form onSubmit={handleAddCardSecure}>
          <div className="modal-body">
            <div className="form-group mb-3">
              <label>Cardholder Name</label>
              <input
                type="text"
                className="form-control"
                name="cardName"
                placeholder="Name on Card"
                value={newCard.cardName}
                onChange={handleCardInputChange}
                required
              />
            </div>
            
            <div className="form-group mb-3">
              <label>Card Details</label>
              <div 
                ref={cardElementRef}
                className="form-control"
                style={{
                  padding: '0.75rem',
                  minHeight: '45px'
                }}
              />
              <small className="form-text text-muted">
                Enter your card number, expiration date, and CVC
              </small>
            </div>
            
            <div className="alert alert-info">
              <i className="fa fa-lock me-2" style={{paddingRight: '0.5rem'}}></i>
              <strong>Secure Payment:</strong> Your card details are encrypted and transmitted directly to Stripe. 
              We never see or store your full card number.
            </div>
            
            <div className="alert alert-warning">
              <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
              This payment method will be used for automatic renewals when your services are due.
            </div>
          </div>
          
          <div className="modal-footer">
            <button 
              type="button" 
              className="btn btn-secondary" 
              onClick={toggleAddCardModal}
              disabled={addingCard}
            >
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={addingCard || !stripe}
            >
              {addingCard ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Processing...
                </>
              ) : (
                <>
                  <i className="fa fa-lock me-1" style={{paddingRight: '0.5rem'}}></i>Add Card Securely
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
)}


          {/* Add PayPal Modal */}
          {showAddPayPalModal && (
            <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
              <div className="modal-dialog" role="document">
                <div className="modal-content">
                  <div className="modal-header">
                    <h5 className="modal-title">
                      <i className="fa fa-paypal me-2" style={{paddingRight: '0.5rem'}}></i>Add PayPal Payment Method
                    </h5>
                    <button type="button" className="close" onClick={() => setShowAddPayPalModal(false)}>
                      <span aria-hidden="true">&times;</span>
                    </button>
                  </div>
                  <form onSubmit={handleAddPayPal}>
                    <div className="modal-body">
                      <div className="text-center mb-4">
                        <i className="fa fa-paypal fa-4x text-primary mb-3"></i>
                      </div>
                      
                      <div className="alert alert-info">
                        <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                        You will be redirected to PayPal to authorize automatic payments. This will allow us to process future payments automatically for your services.
                      </div>
                      
                      <div className="alert alert-warning">
                        <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
                        <strong>Note:</strong> Make sure your PayPal account has sufficient funds or a linked payment method for automatic renewals.
                      </div>
                    </div>
                    
                    <div className="modal-footer">
                      <button 
                        type="button" 
                        className="btn btn-secondary" 
                        onClick={() => setShowAddPayPalModal(false)}
                        disabled={addingPayPal}
                      >
                        Cancel
                      </button>
                      <button 
                        type="submit" 
                        className="btn btn-primary"
                        disabled={addingPayPal}
                      >
                        {addingPayPal ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Processing...
                          </>
                        ) : (
                          <>
                            <i className="fa fa-paypal me-1" style={{paddingRight: '0.5rem'}}></i>Connect PayPal
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          )}




          <div className="card">
            <div className="card-header">
              <div className="card-title">Billing</div>
              <div className="card-options">
                <Link to="/billing/credit" className="btn btn-success btn-sm">Add Credit</Link>&nbsp;
              </div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      <li>
                        <Link 
                          to="/billing/credit" 
                          className={(splitLastOccurrence(path.pathname, '/') === 'credit' || path.pathname === '/billing') ? "active" : ""}
                        >
                          <i className="si si-wallet me-1"></i> Credit
                        </Link>
                      </li>
                      <li>
                        <Link 
                          to="/billing/invoices" 
                          className={splitLastOccurrence(path.pathname, '/') === 'invoices' ? "active" : ""}
                        >
                          <i className="si si-doc me-1"></i> Invoices
                        </Link>
                      </li>
                      <li>
                        <Link 
                          to="/billing/history" 
                          className={splitLastOccurrence(path.pathname, '/') === 'history' ? "active" : ""}
                        >
                          <i className="si si-clock me-1"></i> Payments History
                        </Link>
                      </li>
                      <li>
                        <Link 
                          to="/billing/methods" 
                          className={splitLastOccurrence(path.pathname, '/') === 'methods' ? "active" : ""}
                        >
                          <i className="si si-credit-card me-1"></i> Payment Methods
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
                
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {/* Credit Section */}
                    {(splitLastOccurrence(path.pathname, '/') === 'credit' || path.pathname === '/billing') && (
                      <div className="tab-pane active">
                        {creditAccess === null ? (
                          // Loading state
                          <div className="d-flex justify-content-center p-5">
                            <div className="spinner-border text-primary" role="status">
           
                            </div>
                          </div>
                        ) : creditAccess === false ? (
                          // No access
                          <NoAccessMessage message={creditAccessError} section="credit information" />
                        ) : (
                          // Has access - show credit content
                          <div className="row">
                            {/* Credit Status Card */}
                            <div className="col-xl-4 col-lg-6 col-md-12">
                              <div className="card">
                                <div className="card-header">
                                  <h3 className="card-title">
                                    <i className="si si-wallet text-primary me-2"></i> Credit Status
                                  </h3>
                                </div>
                                <div className="card-body text-center">
                                  {creditLoading ? (
                                    <div className="d-flex justify-content-center p-5">
                                      <div className="spinner-border text-primary" role="status">
                                      </div>
                                    </div>
                                  ) : (
                                    <>
                                      <div className="mb-3">
                                        <h1 className="display-4 font-weight-bold text-primary">
                                          €{(
                                            parseFloat(creditStats.available_credit || 0) + 
                                            parseFloat(creditStats.free_credit || 0)
                                          ).toFixed(2)}
                                        </h1>
                                        <p className="text-muted">Available Credit</p>
                                      </div>
                                      
                                      <ul className="list-group list-group-flush">
                                        <li className="list-group-item d-flex justify-content-between align-items-center">
                                          <span>Available Credit:</span>
                                          <span className="text-success fw-bold">
                                            €{parseFloat(creditStats.available_credit || 0).toFixed(2)}
                                          </span>
                                        </li>

                                        <li className="list-group-item d-flex justify-content-between align-items-center">
                                          <span>Pending Credit:</span>
                                          <span className="text-warning fw-bold">
                                            €{parseFloat(creditStats.pending_credit || 0).toFixed(2)}
                                          </span>
                                        </li>
                                      </ul>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            {/* Add Credit Card */}
                            <div className="col-xl-5 col-lg-6 col-md-12">
                              <div className="card">
                                <div className="card-header">
                                  <h3 className="card-title">
                                    <i className="si si-plus text-success me-2"></i> Add Credit
                                  </h3>
                                </div>
                                <div className="card-body">
                                  <form onSubmit={(e) => { e.preventDefault(); handleAddCredit(); }}>
                                    <div className="form-group mb-3">
                                      <label className="form-label">Payment Method</label>
                                      <select 
                                        className="form-control" 
                                        value={paymentMethod}
                                        onChange={(e) => setPaymentMethod(e.target.value)}
                                        disabled={addingCredit}
                                      >
                                        <option value="cc">Credit Card</option>
                                        <option value="crypto">Cryptocurrency</option>
                                        <option value="bank">Bank Transfer</option>
                                      </select>
                                    </div>
                                    
                                    <div className="form-group mb-4">
                                      <label className="form-label">
                                        Amount (€)
                                        <span className="text-danger">*</span>
                                      </label>
                                      <input 
                                        type="text" 
                                        className="form-control" 
                                        placeholder="300-500"
                                        required
                                        aria-required="true"
                                        minLength="3"
                                        value={creditAmount}
                                        onChange={(e) => setCreditAmount(e.target.value)}
                                        disabled={addingCredit}
                                      />
                                      <small className="form-text text-muted">
                                        Minimum amount: €300 
                                      </small>
                                    </div>
                                    
                                    <div className="form-footer text-end">
                                      <button 
                                        type="submit" 
                                        className="btn btn-success"
                                        disabled={addingCredit || !creditAmount || parseFloat(creditAmount) < 300}
                                      >
                                        {addingCredit ? (
                                          <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            Processing...
                                          </>
                                        ) : (
                                          <>
                                            <i className="fa fa-plus-circle me-1"></i> Add Credit
                                          </>
                                        )}
                                      </button>
                                    </div>
                                  </form>
                                </div>
                              </div>
                            </div>
                            
                            {/* Credit Benefits Card */}
                            <div className="col-xl-3 col-lg-12 col-md-12">
                              <div className="card">
                                <div className="card-header">
                                  <h3 className="card-title">
                                    <i className="si si-info text-info me-2"></i> Benefits
                                  </h3>
                                </div>
                                <div className="card-body">
                                  <ul className="list-unstyled">
                                    <li className="mb-2">
                                      <i className="fa fa-check-circle text-success me-2" style={{paddingRight: '0.5rem'}}></i>
                                      Automatic payments
                                    </li>
                                    <li className="mb-2">
                                      <i className="fa fa-check-circle text-success me-2" style={{paddingRight: '0.5rem'}}></i>
                                      No service interruptions
                                    </li>
                                    <li className="mb-2">
                                      <i className="fa fa-check-circle text-success me-2" style={{paddingRight: '0.5rem'}}></i>
                                      Consolidated billing
                                    </li>
                                    <li className="mb-2">
                                      <i className="fa fa-check-circle text-success me-2" style={{paddingRight: '0.5rem'}}></i>
                                      Priority payment method
                                    </li>
                                  </ul>
                                  <div className="alert alert-info mt-3 mb-0">
                                    <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                                    Credit payments are prioritized over Stripe/PayPal for automatic renewals when both credit and saved payment methods are available.
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Invoices Section */}
                    {splitLastOccurrence(path.pathname, '/') === 'invoices' && (
                      <div className="tab-pane active">
                        {invoicesAccess === null ? (
                          // Loading state
                          <div className="d-flex justify-content-center p-5">
                            <div className="spinner-border text-primary" role="status">
            
                            </div>
                          </div>
                        ) : invoicesAccess === false ? (
                          // No access
                          <NoAccessMessage message={invoicesAccessError} section="invoices" />
                        ) : (
                          // Has access - show invoices content
                          <div className="row">
                            {/* Billing Summary Card */}
                            <div className="col-xl-3 col-lg-6 col-md-12">
                              <div className="card">
                                <div className="card-body">
                                  <div className="d-flex align-items-center">
                                    <div className="me-3">
                                      <span className="avatar avatar-lg bg-danger-transparent">
                                        <i className="fa fa-exclamation-triangle text-danger"></i>
                                      </span>
                                    </div>
                                    <div>
                                      <h2 className="mb-0 font-weight-bold text-danger" style={{paddingLeft: '1.5rem'}}>
                                        €{parseFloat(billingStats.unpaid_amount).toFixed(2)}
                                      </h2>
                                      <span className="text-muted"style={{paddingLeft: '1.5rem'}}>Unpaid Invoices</span>
                                    </div>
                                  </div>
                                  <p className="mt-3 mb-4 text-muted">
                                    Please make sure to pay the outstanding amounts before the due dates to avoid suspension of your services.
                                  </p>
                                </div>
                              </div>
                            </div>
                            
                            {/* Invoices DataTable */}
                            <div className="col-xl-9 col-lg-6 col-md-12">
                              <div className="card smaller">
                                <div className="card-header">
                                  <h3 className="card-title">Invoices</h3>
                                  <div className="card-options">
                                    <button className="btn btn-outline-primary btn-sm">
                                      <i className="fa fa-download me-1"></i> Export
                                    </button>
                                  </div>
                                </div>
                                <div className="card-body p-0">
                                  <DataTable 
                                    value={bills} 
                                    onRowClick={(e) => { routeChange('/billing/invoices/'+e.data.id) }}  
                                    sortField="id" 
                                    sortOrder={-1} 
                                    paginator 
                                    rows={10} 
                                    rowsPerPageOptions={[5, 10, 25, 50]} 
                                    className="border-0"
                                    emptyMessage="No invoices found"
                                    loading={billsLoading}
                                  >
                                    <Column field="id" sortable header="Invoice #" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="type" sortable header="Type" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="total" body={amountFormatter} sortable header="Amount" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="date" sortable header="Issued" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="duedate" sortable header="Due Date" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="status" body={statusFormatter} sortable header="Status" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                  </DataTable>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Payment History Section */}
                    {splitLastOccurrence(path.pathname, '/') === 'history' && (
                      <div className="tab-pane active">
                        {billingAccess === null ? (
                          // Loading state
                          <div className="d-flex justify-content-center p-5">
                            <div className="spinner-border text-primary" role="status">
    
                            </div>
                          </div>
                        ) : billingAccess === false ? (
                          // No access
                          <NoAccessMessage message={billingAccessError} section="payment history" />
                        ) : (
                          // Has access - show payment history content
                          <div className="row">
                            {/* Payment Statistics */}
                            <div className="col-12 mb-4">
                              <div className="row">
                                <div className="col-xl-3 col-lg-6 col-md-6">
                                  <div className="card">
                                    <div className="card-body">
                                      <div className="d-flex">
                                        <div className="me-4">
                                          <span className="avatar avatar-lg bg-primary-transparent">
                                            <i className="si si-credit-card text-primary"></i>
                                          </span>
                                        </div>
                                        <div>
                                          <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Monthly Spending</h6>
                                          <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>
                                            €{parseFloat(billingStats.monthly_spending).toFixed(2)}
                                          </h2>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="col-xl-3 col-lg-6 col-md-6">
                                  <div className="card">
                                    <div className="card-body">
                                      <div className="d-flex">
                                        <div className="me-4">
                                          <span className="avatar avatar-lg bg-success-transparent">
                                            <i className="si si-graph text-success"></i>
                                          </span>
                                        </div>
                                        <div>
                                          <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Yearly Spending</h6>
                                          <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>
                                            €{parseFloat(billingStats.yearly_spending).toFixed(2)}
                                          </h2>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="col-xl-3 col-lg-6 col-md-6">
                                  <div className="card">
                                    <div className="card-body">
                                      <div className="d-flex">
                                        <div className="me-4">
                                          <span className="avatar avatar-lg bg-warning-transparent">
                                            <i className="si si-clock text-warning"></i>
                                          </span>
                                        </div>
                                        <div>
                                          <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Last Payment</h6>
                                          <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>
                                            €{parseFloat(billingStats.last_payment).toFixed(2)}
                                          </h2>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="col-xl-3 col-lg-6 col-md-6">
                                  <div className="card">
                                    <div className="card-body">
                                      <div className="d-flex">
                                        <div className="me-4">
                                          <span className="avatar avatar-lg bg-info-transparent">
                                            <i className="si si-layers text-info"></i>
                                          </span>
                                        </div>
                                        <div>
                                          <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Active Services</h6>
                                          <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>
                                            {billingStats.active_services}
                                          </h2>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            {/* Payment History DataTable */}
                            <div className="col-12">
                              <div className="card">
                                <div className="card-header">
                                  <h3 className="card-title">
                                    <i className="si si-refresh text-primary me-2"></i>Payment History
                                  </h3>
                                  <div className="card-options">
                                    <button className="btn btn-outline-primary btn-sm">
                                      <i className="fa fa-download me-1"></i> Export
                                    </button>
                                  </div>
                                </div>
                                <div className="card-body p-0">
                                  <DataTable 
                                    value={history} 
                                    onRowClick={(e) => { routeChange('/billing/history/'+e.data.id) }}  
                                    sortField="id" 
                                    sortOrder={-1} 
                                    paginator 
                                    rows={10} 
                                    rowsPerPageOptions={[5, 10, 25, 50]} 
                                    className="border-0"
                                    emptyMessage="No payment history found"
                                    loading={historyLoading}
                                  >
                                    <Column field="id" sortable header="Transaction ID" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="total" body={amountFormatter} sortable header="Amount" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="date" sortable header="Date Paid" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="method" sortable header="Payment Method" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="status" body={statusFormatter} sortable header="Status" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                  </DataTable>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Payment Methods Section */}
                    {splitLastOccurrence(path.pathname, '/') === 'methods' && (
                      <div className="tab-pane active">
                        {billingAccess === null ? (
                          // Loading state
                          <div className="d-flex justify-content-center p-5">
                            <div className="spinner-border text-primary" role="status">
                 
                            </div>
                          </div>
                        ) : billingAccess === false ? (
                          // No access
                          <NoAccessMessage message={billingAccessError} section="payment methods" />
                        ) : (
                          // Has access - show payment methods content
                          <div className="row">
                            <div className="col-md-4">
                              <div className="card">
                                <div className="card-body">
                                  <h4 className="mb-3">
                                    <i className="fa fa-plus-circle text-success me-2" style={{paddingRight: '0.5rem'}}></i>Add Payment Method
                                  </h4>
                                  <p className="text-muted mb-4">
                                    Save your payment methods for automatic service renewals. You can add both Stripe (credit cards) and PayPal payment methods.
                                  </p>
                                  <div className="d-grid gap-2">
                                    <button 
                                      className="btn btn-primary" 
                                      onClick={toggleAddCardModal}
                                    >
                                      <i className="fa fa-cc-stripe me-2"></i> Add Card
                                    </button>
                                    <button 
                                      className="btn btn-info" 
                                      onClick={() => setShowAddPayPalModal(true)}
                                    >
                                      <i className="fa fa-paypal me-2"></i> Add PayPal
                                    </button>


                                  </div>
                                  <div className="alert alert-warning mt-3 mb-0">
                                    <small>
                                      <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                                      Payment methods are used for automatic renewals when services have auto-renewal enabled.
                                    </small>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="col-md-8">
                              <div className="card">
                                <div className="card-body">
                                  <h4 className="mb-3">
                                    <i className="fa fa-credit-card text-primary me-2"style={{paddingRight: '0.5rem'}}></i>Saved Payment Methods
                                  </h4>
                                  
                                  {cardsLoading ? (
                                    <div className="d-flex justify-content-center p-5">
                                      <div className="spinner-border text-primary" role="status">
                                      </div>
                                    </div>
                                  ) : (
                                    cards.length > 0 ? (
                                      <div className="table-responsive">
                                        <table className="table table-hover">
                                          <thead>
                                            <tr>
                                              <th>Processor</th>
                                              <th>Payment Method</th>
                                              <th>Status</th>
                                              <th>Default</th>
                                              <th>Actions</th>
                                            </tr>
                                          </thead>
                                          <tbody>
                                            {cards.map(card => (
                                              <tr key={card.id}>
                                                <td>
                                                  {card.processor === 'stripe' ? (
                                                    <span>
                                                      <i className="fa fa-cc-stripe text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                                                      Stripe
                                                    </span>
                                                                                    ) : card.processor === 'paypal' ? (
                                    <span>
                                      <i className="fa fa-paypal text-info me-2" style={{paddingRight: '0.5rem'}}></i>
                                      PayPal
                                    </span>
                                  ) : (
                                    <span>
                                      <i className="fa fa-credit-card me-2" style={{paddingRight: '0.5rem'}}></i>
                                      {card.processor}
                                    </span>
                                  )}
                                                </td>
                                                <td>
                                                  {card.processor === 'stripe' ? (
                                                    <span>
                                                      {card.brand && (
                                                        <i className={`fa fa-cc-${card.brand.toLowerCase()} me-1`} style={{paddingRight: '0.5rem'}}></i>
                                                      )}
                                                      {card.last_four ? `**** ${card.last_four}` : 'Card'}
                                                    </span>
                                                                                    ) : card.processor === 'paypal' ? (
                                    <span>
                                      {card.payer_email || 'PayPal Account'}
                                    </span>
                                  ) : (
                                    <span>{card.description || 'Payment Method'}</span>
                                  )}
                                                </td>
                                                <td>
                                                  <span className={`badge ${card.status === 'active' ? 'bg-success' : 'bg-secondary'}`}>
                                                    {card.status}
                                                  </span>
                                                </td>
                                                <td>
                                                  {parseInt(card.is_default) === 1 ? (
                                                    <span className="badge bg-primary">
                                                      <i className="fa fa-star me-1" style={{paddingRight: '0.5rem'}}></i>Default
                                                    </span>
                                                  ) : (
                                                    <button 
                                                      className="btn btn-sm btn-outline-secondary"
                                                      onClick={() => handleSetDefaultPaymentMethod(card.id)}
                                                      title="Set as default"
                                                    >
                                                      <i className="fa fa-star"></i>
                                                    </button>
                                                  )}
                                                </td>
                                                <td>
                                                  <div className="btn-group">
                                                    <button 
                                                      className="btn btn-sm btn-danger"
                                                      onClick={() => handleRemovePaymentMethod(card.id)}
                                                      title="Remove payment method"
                                                    >
                                                      <i className="fa fa-trash"></i>
                                                    </button>
                                                  </div>
                                                </td>
                                              </tr>
                                            ))}
                                          </tbody>
                                        </table>
                                      </div>
                                    ) : (
                                      <div className="text-center p-4">
                                        <i className="fa fa-credit-card fa-3x text-muted mb-3"></i>
                                        <h5 className="text-muted">No Payment Methods</h5>
                                        <p className="text-muted">Add a payment method to enable automatic renewals for your services.</p>
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Billing;