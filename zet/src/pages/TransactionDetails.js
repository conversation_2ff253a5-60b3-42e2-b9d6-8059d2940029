import React, { useState, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import DiscountAlert from "../components/DiscountAlert";


const TransactionDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sample transaction data - in a real application, this would come from an API
  const sampleTransaction = {
    id: id,
    transactionId: `TRX-${id}`,
    status: "Paid",
    amount: "349.00",
    date: "2025-02-15 14:32:45",
    method: "Credit Card",
    cardType: "Mastercard",
    cardLast4: "2763",
    relatedInvoices: [
      {
        id: "12345",
        number: "INV-12345",
        date: "2025-02-10",
        amount: "349.00",
        description: "Dedicated Server - LA-BL1S3 (March 2025)"
      }
    ],
    billingInfo: {
      name: "<PERSON>",
      company: "Example Corp",
      address: "123 Main Street",
      city: "New York",
      country: "United States",
      vatId: "EU123456789"
    }
  };

  useEffect(() => {
    // Simulate API call to fetch transaction details
    const fetchTransactionDetails = () => {
      setLoading(true);
      // In a real app, you would fetch from an API
      // fetch(`/New_client/api.php?f=transaction&id=${id}`)
      //   .then(response => response.json())
      //   .then(data => {
      //     setTransaction(data);
      //     setLoading(false);
      //   })
      //   .catch(error => {
      //     console.error('Error fetching transaction details:', error);
      //     setLoading(false);
      //   });
      
      // For demo purposes, use sample data
      setTimeout(() => {
        setTransaction(sampleTransaction);
        setLoading(false);
      }, 500);
    };

    fetchTransactionDetails();
  }, [id]);

  const getStatusBadge = (status) => {
    switch(status) {
      case 'Paid':
        return <span className="badge bg-success">Paid</span>;
      case 'Failed':
        return <span className="badge bg-danger">Failed</span>;
      case 'Pending':
        return <span className="badge bg-warning">Pending</span>;
      case 'Refunded':
        return <span className="badge bg-info">Refunded</span>;
      default:
        return <span className="badge bg-secondary">{status}</span>;
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "300px" }}>
        <div className="spinner-border text-primary" role="status">
          
        </div>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="alert alert-danger" role="alert">
        Transaction not found. <Link to="/billing/history">Return to payment history</Link>
      </div>
    );
  }

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1"><Link to="/billing/history">Payment History</Link></li>
            <li className="breadcrumb-item1">Transaction #{transaction.id}</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card">
            <div className="card-header">
              <div className="card-title">
                Transaction #{transaction.transactionId}
                <span className="ms-2">
                  {getStatusBadge(transaction.status)}
                </span>
              </div>
              <div className="card-options">
                <button className="btn btn-outline-primary btn-sm me-2">
                  <i className="fa fa-download me-1"></i> Download Receipt
                </button>
                <button className="btn btn-outline-secondary btn-sm">
                  <i className="fa fa-print me-1"></i> Print
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-lg-6">
                  <div className="card">
                    <div className="card-header">
                      <h3 className="card-title">Transaction Details</h3>
                    </div>
                    <div className="card-body">
                      <table className="table table-bordered">
                        <tbody>
                          <tr>
                            <td className="fw-bold">Transaction ID</td>
                            <td>{transaction.transactionId}</td>
                          </tr>
                          <tr>
                            <td className="fw-bold">Amount</td>
                            <td>€{transaction.amount}</td>
                          </tr>
                          <tr>
                            <td className="fw-bold">Date</td>
                            <td>{transaction.date}</td>
                          </tr>
                          <tr>
                            <td className="fw-bold">Status</td>
                            <td>{getStatusBadge(transaction.status)}</td>
                          </tr>
                          <tr>
                            <td className="fw-bold">Payment Method</td>
                            <td>
                              {transaction.method}
                              {transaction.cardType && (
                                <> ({transaction.cardType} ending in {transaction.cardLast4})</>
                              )}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                
                <div className="col-lg-6">
                  <div className="card">
                    <div className="card-header">
                      <h3 className="card-title">Billing Information</h3>
                    </div>
                    <div className="card-body">
                      <address>
                        <strong>{transaction.billingInfo.name}</strong><br />
                        {transaction.billingInfo.company && (
                          <>{transaction.billingInfo.company}<br /></>
                        )}
                        {transaction.billingInfo.address}<br />
                        {transaction.billingInfo.city}, {transaction.billingInfo.country}<br />
                        {transaction.billingInfo.vatId && (
                          <>VAT ID: {transaction.billingInfo.vatId}<br /></>
                        )}
                      </address>
                    </div>
                  </div>
                </div>
              </div>

              <div className="row mt-4">
                <div className="col-12">
                  <div className="card">
                    <div className="card-header">
                      <h3 className="card-title">Related Invoices</h3>
                    </div>
                    <div className="card-body">
                      <div className="table-responsive">
                        <table className="table table-hover table-bordered">
                          <thead>
                            <tr>
                              <th>Invoice #</th>
                              <th>Date</th>
                              <th>Description</th>
                              <th className="text-end">Amount</th>
                              <th>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {transaction.relatedInvoices.map(invoice => (
                              <tr key={invoice.id}>
                                <td>{invoice.number}</td>
                                <td>{invoice.date}</td>
                                <td>{invoice.description}</td>
                                <td className="text-end">€{invoice.amount}</td>
                                <td>
                                  <Link to={`/billing/invoices/${invoice.id}`} className="btn btn-sm btn-outline-primary">
                                    View
                                  </Link>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="card-footer text-end">
              <Link to="/billing/history" className="btn btn-outline-secondary">
                <i className="fa fa-arrow-left me-1"></i> Back to Payment History
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TransactionDetails;