import React, { useState, useEffect } from "react"
import { Outlet, Link, useLocation } from "react-router-dom";
import { Toast } from 'primereact/toast';
import DiscountAlert from "../components/DiscountAlert";

const Account = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('details');
  const toast = React.useRef(null);

  // Helper function to determine active tab
  const isActiveTab = (tabName) => {
    return location.pathname.split('/').pop() === tabName ? 'active' : '';
  };

  // Account details sections
  const accountSections = [
    { 
      name: 'details', 
      label: 'Profile Details', 
      icon: 'si si-user',
      content: <ProfileDetailsSection toast={toast} />
    },
    { 
      name: 'password', 
      label: 'Password', 
      icon: 'si si-lock',
      content: <PasswordSection toast={toast} /> 
    },
    { 
      name: 'api', 
      label: 'API Access', 
      icon: 'si si-settings',
      content: <APISection toast={toast} /> 
    },

    { 
      name: 'access', 
      label: 'Access Management', 
      icon: 'si si-shield',
      content: <AccessManagementSection toast={toast} /> 
    }
  ];

  return (
    <>
      <Toast ref={toast} />
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">Account</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <div className="row">
      <div className="col-12">
        <DiscountAlert />

          <div className="card smaller">
            <div className="card-header">
              <div className="card-title">Account</div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                    {accountSections.map(section => (
                      <li key={section.name}>
                        <Link 
                          to={`/account/${section.name}`} 
                          className={isActiveTab(section.name)}
                        >
                          <i className={`${section.icon}`} style={{ marginRight: '8px', verticalAlign: 'middle' }}></i>
                          {section.label}
                        </Link>
                      </li>
                    ))}
                    </ul>
                  </div>
                </div>
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {accountSections.find(section => 
                      isActiveTab(section.name)
                    )?.content}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

// Updated ProfileDetailsSection to use real database data
const ProfileDetailsSection = ({ toast }) => {
  // Form data state with empty initial values
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    company_name: '',
    vat_id: '',
    email: '',
    address: '',
    city: '',
    country: ''
  });
  
  const [loading, setLoading] = useState(true);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  // Function to get token from sessionStorage
  function getToken() {
    return sessionStorage.getItem('token');
  }

  // Fetch user data from API
  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      setErrorMessage('');
      
      try {
        const response = await fetch("/api.php?f=user_details", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ token: getToken() })
        });
        
        const data = await response.json();
        
        if (data.error) {
          if (data.error === 5) {
            toast.current.show({
              severity: 'error',
              summary: 'Session Timeout',
              detail: 'Your login session has timed out',
              life: 5000
            });
            window.location.reload(false);
          } else {
            setErrorMessage(`Error fetching user data: ${data.error}`);
          }
        } else {
          // Map API response to form data
          setFormData({
            firstName: data.first_name || '',
            lastName: data.last_name || '',
            company_name: data.company_name || '',
            vat_id: data.vat_id || '',
            email: data.email || '',
            address: data.address || '',
            city: data.city || '',
            country: data.country || ''
          });
        }
      } catch (error) {
        setErrorMessage(`Failed to fetch user data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Update user profile data
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      const response = await fetch("/api.php?f=update_user", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getToken(),
          first_name: formData.firstName,
          last_name: formData.lastName,
          company_name: formData.company_name,
          vat_id: formData.vat_id,
          email: formData.email,
          address: formData.address,
          city: formData.city,
          country: formData.country
        })
      });
      
      const data = await response.json();
      
              if (data.error) {
          if (data.error === 5) {
            toast.current.show({
              severity: 'error',
              summary: 'Session Timeout',
              detail: 'Your login session has timed out',
              life: 5000
            });
            window.location.reload(false);
          } else {
            setErrorMessage(`Error updating profile: ${data.error}`);
          }
        } else {
        setSuccessMessage('Profile updated successfully!');
        // Set a timeout to clear the success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
      }
    } catch (error) {
      setErrorMessage(`Failed to update profile: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !formData.email) {
    return (
      <div className="tab-pane active">
        <div className="d-flex justify-content-center p-5">
          <div className="spinner-border text-primary" role="status">
     
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tab-pane active">
      {errorMessage && (
        <div className="alert alert-danger" role="alert">
          <i className="fa fa-exclamation-circle me-2"></i>
          {errorMessage}
        </div>
      )}
      
      {successMessage && (
        <div className="alert alert-success" role="alert">
          <i className="fa fa-check-circle-o me-2"></i>
          {successMessage}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="row">
          <div className="col-md-6">
            <div className="form-group">
              <label>First Name</label>
              <input 
                type="text" 
                className="form-control" 
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                placeholder="First Name"
                required
              />
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-group">
              <label>Last Name</label>
              <input 
                type="text" 
                className="form-control" 
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                placeholder="Last Name"
                required
              />
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-group">
              <label>Company (Optional)</label>
              <input 
                type="text" 
                className="form-control" 
                name="company_name"
                value={formData.company_name}
                onChange={handleChange}
                placeholder="Company Name"
              />
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-group">
              <label>VAT ID (EU Companies)</label>
              <input 
                type="text" 
                className="form-control" 
                name="vat_id"
                value={formData.vat_id}
                onChange={handleChange}
                placeholder="VAT ID"
              />
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-group">
              <label>Email</label>
              <input 
                type="email" 
                className="form-control" 
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Email Address"
                required
              />
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-group">
              <label>Address</label>
              <input 
                type="text" 
                className="form-control" 
                name="address"
                value={formData.address}
                onChange={handleChange}
                placeholder="Home Address"
                required
              />
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-group">
              <label>City</label>
              <input 
                type="text" 
                className="form-control" 
                name="city"
                value={formData.city}
                onChange={handleChange}
                placeholder="City"
                required
              />
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-group">
              <label>Country</label>
              <select 
                className="form-control" 
                name="country"
                value={formData.country}
                onChange={handleChange}
                required
              >
                <option value="">Select Country</option>
                <option value="AT">Austria</option>
                <option value="BE">Belgium</option>
                <option value="BG">Bulgaria</option>
                <option value="HR">Croatia</option>
                <option value="CY">Cyprus</option>
                <option value="CZ">Czech Republic</option>
                <option value="DK">Denmark</option>
                <option value="EE">Estonia</option>
                <option value="FI">Finland</option>
                <option value="FR">France</option>
                <option value="DE">Germany</option>
                <option value="GR">Greece</option>
                <option value="HU">Hungary</option>
                <option value="IE">Ireland</option>
                <option value="IT">Italy</option>
                <option value="LV">Latvia</option>
                <option value="LT">Lithuania</option>
                <option value="LU">Luxembourg</option>
                <option value="MT">Malta</option>
                <option value="NL">Netherlands</option>
                <option value="PL">Poland</option>
                <option value="PT">Portugal</option>
                <option value="RO">Romania</option>
                <option value="SK">Slovakia</option>
                <option value="SI">Slovenia</option>
                <option value="ES">Spain</option>
                <option value="SE">Sweden</option>
                <option value="GB">United Kingdom</option>
                <option value="US">United States</option>
                <option value="CA">Canada</option>
                <option value="AU">Australia</option>
                <option value="JP">Japan</option>
                <option value="SG">Singapore</option>
                <option value="CH">Switzerland</option>
                <option value="NO">Norway</option>
              </select>
            </div>
          </div>
        </div>
        <div className="form-footer">
          <div className="mb-3">
            <b>*If you are not representing a company, please leave the "Company" and "VAT ID" fields empty.<br/>**Tax exempt companies are validated automatically if the entered VAT ID is valid</b>
          </div>
          <div className="text-right">
            <button 
              type="submit" 
              className="btn btn-primary" 
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Updating...
                </>
              ) : 'Update Profile'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

// Updated PasswordSection to use real database interaction
const PasswordSection = ({ toast }) => {
  const [passwords, setPasswords] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  // Function to get token from sessionStorage
  function getToken() {
    return sessionStorage.getItem('token');
  }

  const handleChange = (e) => {
    const { name, value } = e.target;
    setPasswords(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Basic validation
    if (passwords.newPassword !== passwords.confirmNewPassword) {
      setErrorMessage('New passwords do not match');
      return;
    }

    if (passwords.newPassword.length < 8) {
      setErrorMessage('Password must be at least 8 characters long');
      return;
    }

    setLoading(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      const response = await fetch("/api.php?f=change_password", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getToken(),
          current_password: passwords.currentPassword,
          new_password: passwords.newPassword
        })
      });
      
      const data = await response.json();
      
      if (data.error) {
        if (data.error === 5) {
          toast.current.show({
            severity: 'error',
            summary: 'Session Timeout',
            detail: 'Your login session has timed out',
            life: 5000
          });
          window.location.reload(false);
        } else if (data.error === 1) {
          setErrorMessage('Current password is incorrect');
        } else {
          setErrorMessage(`Error changing password: ${data.error}`);
        }
      } else {
        setSuccessMessage('Password changed successfully!');
        setPasswords({
          currentPassword: '',
          newPassword: '',
          confirmNewPassword: ''
        });
        
        // Set a timeout to clear the success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
      }
    } catch (error) {
      setErrorMessage(`Failed to change password: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="tab-pane active">
      {errorMessage && (
        <div className="alert alert-danger" role="alert">
          <i className="fa fa-exclamation-circle me-2"></i>
          {errorMessage}
        </div>
      )}
      
      {successMessage && (
        <div className="alert alert-success" role="alert">
          <i className="fa fa-check-circle-o me-2"></i>
          {successMessage}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="row">
          <div className="col-md-12">
            <div className="form-group">
              <label>Current Password</label>
              <input 
                type="password" 
                className="form-control" 
                name="currentPassword"
                value={passwords.currentPassword}
                onChange={handleChange}
                placeholder="Current Password"
                required
              />
            </div>
          </div>
          <div className="col-md-12">
            <div className="form-group">
              <label>New Password</label>
              <input 
                type="password" 
                className="form-control" 
                name="newPassword"
                value={passwords.newPassword}
                onChange={handleChange}
                placeholder="New Password"
                required
              />
              <small className="form-text text-muted">
                Password must be at least 8 characters long and include uppercase, lowercase, and numbers
              </small>
            </div>
          </div>
          <div className="col-md-12">
            <div className="form-group">
              <label>Confirm New Password</label>
              <input 
                type="password" 
                className="form-control" 
                name="confirmNewPassword"
                value={passwords.confirmNewPassword}
                onChange={handleChange}
                placeholder="Confirm New Password"
                required
              />
            </div>
          </div>
        </div>
        <div className="form-footer text-right">
          <button 
            type="submit" 
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Changing Password...
              </>
            ) : 'Change Password'}
          </button>
        </div>
      </form>
    </div>
  );
};

// API Access Section (Keep existing code, no need to fetch from DB as it uses client-side state)
const APISection = ({ toast }) => {
	const [apiKeys, setApiKeys] = useState([]);
	const [currentApiKey, setCurrentApiKey] = useState('');
	const [regenerating, setRegenerating] = useState(false);
	const [showPasswordModal, setShowPasswordModal] = useState(false);
	const [secretPassword, setSecretPassword] = useState('');
	const [apiKeyName, setApiKeyName] = useState('');
	const [showCreateModal, setShowCreateModal] = useState(false);
	const [loading, setLoading] = useState(true);
	
	// Add access control state
	const [hasAccess, setHasAccess] = useState(null); // Start with null to show loading
	const [accessError, setAccessError] = useState('');
	
	// Function to get token from sessionStorage
	function getToken() {
		return sessionStorage.getItem('token');
	}

	// No Access Component
	const NoAccessMessage = () => (
		<div className="text-center p-5">
			<i className="fa fa-lock fa-4x text-danger mb-4"></i>
			<h3 className="text-danger mb-3">Access Denied</h3>
			<p className="text-muted mb-4" style={{fontSize: '1.1rem'}}>
				{accessError || 'You do not have permission to access API management.'}
			</p>
			<p className="text-muted">
				Please contact your administrator if you believe this is an error.
			</p>
			<div className="mt-4">
				<button 
					className="btn btn-secondary me-2" 
					onClick={() => window.location.reload()}
				>
					<i className="fa fa-refresh me-1"></i> Refresh Page
				</button>
			</div>
		</div>
	);
  
	// Fetch API keys when component mounts
	useEffect(() => {
		const fetchApiKeys = async () => {
			setLoading(true);
			try {
				const response = await fetch("/api.php?f=get_api_keys", {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ token: getToken() })
				});
				
				console.log("API Keys Response status:", response.status);
				
				// Check for 403 status code BEFORE trying to parse JSON
				if (response.status === 403) {
					console.log("403 detected - setting no access for API keys");
					setHasAccess(false);
					setAccessError('You do not have permission to access API keys');
					setLoading(false);
					return;
				}
				
				// Check if response is ok for other status codes
				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}
				
				const data = await response.json();
				
				console.log("API Keys Response Data:", data);
				
				// Additional check for error messages in the JSON response
				if (data.error) {
					if (data.error === 5) {
						toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
						window.location.reload(false);
					} else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
						// Handle permission errors in JSON response
						console.log("Permission error in JSON response");
						setHasAccess(false);
						setAccessError(data.message || 'Access denied');
						setLoading(false);
						return;
					} else {
						console.error('API error:', data.error);
						setHasAccess(true); // Allow page to show for other errors
					}
				} else if (Array.isArray(data)) {
					// Success - data loaded properly
					console.log("API keys data loaded successfully");
					setApiKeys(data);
					setHasAccess(true);
				}
			} catch (error) {
				console.error('Failed to fetch API keys:', error);
				// For network errors, still show the page
				setHasAccess(true);
			}
			setLoading(false);
		};

		fetchApiKeys();
	}, []);
  
	// Generate a random string of specified length
	const generateRandomString = (length) => {
		const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		let result = '';
		for (let i = 0; i < length; i++) {
			result += chars.charAt(Math.floor(Math.random() * chars.length));
		}
		return result;
	};
  
	const handleCreateNewKey = () => {
		setApiKeyName('');
		setShowCreateModal(true);
	};
  
	const handleSubmitNewKey = async () => {
		if (!apiKeyName.trim()) {
			toast.current.show({
				severity: 'error',
				summary: 'Invalid Input',
				detail: 'Please enter a name for your API key',
				life: 3000
			});
			return;
		}
		
		setShowCreateModal(false);
		setRegenerating(true);
		
		try {
			const response = await fetch("/api.php?f=create_api_key", {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ 
					token: getToken(),
					key_name: apiKeyName
				})
			});
			
			// Handle 403 response
			if (response.status === 403) {
				toast.current.show({
					severity: 'error',
					summary: 'Access Denied',
					detail: 'You do not have permission to create API keys',
					life: 5000
				});
				setRegenerating(false);
				return;
			}
			
			const data = await response.json();
			
			if (data.error) {
				if (data.error === 5) {
					toast.current.show({
						severity: 'error',
						summary: 'Session Timeout',
						detail: 'Your login session has timed out',
						life: 5000
					});
					window.location.reload(false);
				} else {
					toast.current.show({
						severity: 'error',
						summary: 'API Error',
						detail: `Error creating API key: ${data.error}`,
						life: 5000
					});
				}
				setRegenerating(false);
			} else {
				// Update the API keys list with the new key
				const newKey = {
					id: data.id,
					name: apiKeyName,
					key: data.api_key,
					created: new Date().toLocaleString(),
					lastUsed: 'Never'
				};
				
				setApiKeys([...apiKeys, newKey]);
				setCurrentApiKey(data.api_key);
				setSecretPassword(data.secret);
				setRegenerating(false);
				setShowPasswordModal(true);
			}
		} catch (error) {
			toast.current.show({
				severity: 'error',
				summary: 'Connection Error',
				detail: `Failed to create API key: ${error.message}`,
				life: 5000
			});
			setRegenerating(false);
		}
	};
  
	const handleRegenerateAPI = async (keyId) => {
		setRegenerating(true);
		
		try {
			const response = await fetch("/api.php?f=regenerate_api_key", {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ 
					token: getToken(),
					key_id: keyId
				})
			});
			
			// Handle 403 response
			if (response.status === 403) {
				toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Access denied: You do not have permission to regenerate API keys',
        life: 3000
      });
				setRegenerating(false);
				return;
			}
			
			const data = await response.json();
			
			if (data.error) {
				if (data.error === 5) {
					toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
					window.location.reload(false);
				} else {
					toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Error regenerating API key: ${data.error}`,
        life: 5000
      });
				}
				setRegenerating(false);
			} else {
				// Update the specific API key
				const updatedKeys = apiKeys.map(key => {
					if (key.id === keyId) {
						return {
							...key,
							key: data.api_key,
							created: new Date().toLocaleString()
						};
					}
					return key;
				});
				
				setApiKeys(updatedKeys);
				setCurrentApiKey(data.api_key);
				setSecretPassword(data.secret);
				setRegenerating(false);
				setShowPasswordModal(true);
			}
		} catch (error) {
			toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to regenerate API key: ${error.message}`,
        life: 5000
      });
			setRegenerating(false);
		}
	};
  
	const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
	const [keyToDelete, setKeyToDelete] = useState(null);
  
	const handleDeleteKey = (keyId) => {
		setKeyToDelete(keyId);
		setShowDeleteConfirmModal(true);
	};
  
	const confirmDeleteKey = async () => {
		try {
			const response = await fetch("/api.php?f=delete_api_key", {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ 
					token: getToken(),
					key_id: keyToDelete
				})
			});
			
			// Handle 403 response
			if (response.status === 403) {
				toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Access denied: You do not have permission to delete API keys',
        life: 3000
      });
				setShowDeleteConfirmModal(false);
				setKeyToDelete(null);
				return;
			}
			
			const data = await response.json();
			
			if (data.error) {
				if (data.error === 5) {
					toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
					window.location.reload(false);
				} else {
					toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Error deleting API key: ${data.error}`,
        life: 5000
      });
				}
			} else {
				const updatedKeys = apiKeys.filter(key => key.id !== keyToDelete);
				setApiKeys(updatedKeys);
			}
		} catch (error) {
			toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to delete API key: ${error.message}`,
        life: 5000
      });
		}
		
		setShowDeleteConfirmModal(false);
		setKeyToDelete(null);
	};
  
	const cancelDeleteKey = () => {
		setShowDeleteConfirmModal(false);
		setKeyToDelete(null);
	};
  
	const closePasswordModal = () => {
		setShowPasswordModal(false);
	};
  
	const closeCreateModal = () => {
		setShowCreateModal(false);
	};
  
	// Format the key to show only first and last few characters
	const formatApiKey = (key) => {
		if (!key) return '';
		return `${key.slice(0, 6)}...${key.slice(-4)}`;
	};

	// Show loading state
	if (hasAccess === null) {
		return (
			<div className="tab-pane active">
				<div className="row mb-4">
					<div className="col-12">
						<div className="card">
							<div className="card-body">
								<div className="d-flex justify-content-center p-5">
									<div className="spinner-border text-primary" role="status">
	
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Show no access message
	if (hasAccess === false) {
		return (
			<div className="tab-pane active">
				<div className="row mb-4">
					<div className="col-12">
						<div className="card">
							<div className="card-header">
								<h3 className="card-title">API Keys</h3>
							</div>
							<div className="card-body">
								<NoAccessMessage />
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
  
	return (
	  <div className="tab-pane active">
		<div className="row mb-4">
		  <div className="col-12">
			<div className="card">
			  <div className="card-header d-flex justify-content-between align-items-center">
				<h3 className="card-title">API Keys</h3>
				<button 
				  className="btn btn-primary" 
				  onClick={handleCreateNewKey}
				  disabled={regenerating}
				>
				  Create New API Key
				</button>
			  </div>
			  <div className="card-body">
				{loading ? (
					<div className="d-flex justify-content-center p-5">
						<div className="spinner-border text-primary" role="status">
							<span className="sr-only">Loading API keys...</span>
						</div>
					</div>
				) : apiKeys.length === 0 ? (
				  <div className="text-center p-4">
					<p className="text-muted">You don't have any API keys yet.</p>
					<p>API keys allow you to programmatically access our services.</p>
					<button 
					  className="btn btn-primary" 
					  onClick={handleCreateNewKey}
					  disabled={regenerating}
					>
					  Create Your First API Key
					</button>
				  </div>
				) : (
				  <div className="table-responsive">
					<table className="table card-table text-nowrap mb-0">
					  <thead>
						<tr>
						  <th>Name</th>
						  <th>API Key</th>
						  <th>Created</th>
						  <th>Last Used</th>
						  <th>Actions</th>
						</tr>
					  </thead>
					  <tbody>
						{apiKeys.map(key => (
						  <tr key={key.id}>
							<td>{key.name}</td>
							<td>{formatApiKey(key.key)}</td>
							<td>{key.created}</td>
							<td>{key.lastUsed}</td>
							<td>
							  <button 
								className="btn btn-sm btn-danger mr-2" 
								onClick={() => handleRegenerateAPI(key.id)}
								disabled={regenerating}
							  >
								{regenerating ? 'Regenerating...' : 'Regenerate'}
							  </button>
							  <button 
								className="btn btn-sm btn-secondary" 
								onClick={() => handleDeleteKey(key.id)}
							  >
								Delete
							  </button>
							</td>
						  </tr>
						))}
					  </tbody>
					</table>
				  </div>
				)}
		
		{/* Delete Confirmation Modal */}
		{showDeleteConfirmModal && (
		  <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
			<div className="modal-dialog" role="document">
			  <div className="modal-content">
				<div className="modal-header">
				  <h5 className="modal-title">Confirm Delete</h5>
				  <button type="button" className="close" onClick={cancelDeleteKey}>
					<span aria-hidden="true">&times;</span>
				  </button>
				</div>
				<div className="modal-body">
				  <p>Are you sure you want to delete this API key? Any applications using this key will no longer work.</p>
				</div>
				<div className="modal-footer">
				  <button type="button" className="btn btn-secondary" onClick={cancelDeleteKey}>Cancel</button>
				  <button type="button" className="btn btn-danger" onClick={confirmDeleteKey}>Delete API Key</button>
				</div>
			  </div>
			</div>
		  </div>
		)}
				<div className="mt-3">
				  <p className="text-muted small">
					<i className="fa fa-info-circle mr-1"></i>
					API keys provide full access to your account. Keep them secure and never share them in publicly accessible areas.
				  </p>
				</div>
			  </div>
			</div>
		  </div>
		</div>
		
		{/* Secret Password Modal */}
		{showPasswordModal && (
		  <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
			<div className="modal-dialog" role="document">
			  <div className="modal-content">
				<div className="modal-header">
				  <h5 className="modal-title">API Authentication</h5>
				  <button type="button" className="close" onClick={closePasswordModal}>
					<span aria-hidden="true">&times;</span>
				  </button>
				</div>
				<div className="modal-body">
				  <p>Your API key has been generated successfully.</p>
				  <div className="form-group">
					<label><strong>API Key:</strong></label>
					<div className="input-group mb-3">
					  <input 
						type="text" 
						className="form-control" 
						value={currentApiKey}
						readOnly 
					  />
					  <div className="input-group-append">
						<button 
						  className="btn btn-secondary" 
						  onClick={() => {
							navigator.clipboard.writeText(currentApiKey);
							toast.current.show({severity: 'success', summary: 'API Key', detail: 'API key copied to clipboard!'});
						  }}
						>
						  Copy
						</button>
					  </div>
					</div>
				  </div>
				  <div className="form-group">
					<label><strong>Secret Password:</strong></label>
					<div className="input-group">
					  <input 
						type="text" 
						className="form-control" 
						value={secretPassword}
						readOnly 
					  />
					  
					  <div className="input-group-append">
						<button 
						  className="btn btn-secondary" 
						  onClick={() => {
							navigator.clipboard.writeText(secretPassword);
							toast.current.show({severity: 'success', summary: 'Secret Password', detail: 'Password copied to clipboard!'});
						  }}
						>
						  Copy
						</button>
						
					  </div>
					  
					</div>
					
				  </div>
				  <div className="alert alert-warning">
					<strong>Important:</strong> Please save your secret password. It will not be shown again.
				  </div>
				</div>
				<div className="modal-footer">
				  <button type="button" className="btn btn-primary" onClick={closePasswordModal}>I've Saved My Credentials</button>
				</div>
			  </div>
			</div>
		  </div>
		)}
		
		{/* Create New API Key Modal */}
		{showCreateModal && (
		  <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
			<div className="modal-dialog" role="document">
			  <div className="modal-content">
				<div className="modal-header">
				  <h5 className="modal-title">Create New API Key</h5>
				  <button type="button" className="close" onClick={closeCreateModal}>
					<span aria-hidden="true">&times;</span>
				  </button>
				</div>
				<div className="modal-body">
				  <div className="form-group">
					<label><strong>API Key Name:</strong></label>
					<input 
					  type="text" 
					  className="form-control" 
					  placeholder="e.g., Production App, Development, Testing, etc."
					  value={apiKeyName}
					  onChange={(e) => setApiKeyName(e.target.value)}
					/>
					<small className="form-text text-muted">
					  Give your API key a descriptive name to help you identify where it's being used.
					</small>
				  </div>
				</div>
				<div className="modal-footer">
				  <button type="button" className="btn btn-secondary" onClick={closeCreateModal}>Cancel</button>
				  <button type="button" className="btn btn-primary" onClick={handleSubmitNewKey}>Create API Key</button>
				</div>
			  </div>
			</div>
		  </div>
		)}
	  </div>
	);
};


const AccessManagementSection = ({ toast }) => {
  const [users, setUsers] = useState([]);
  const [pendingInvites, setPendingInvites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [activeTab, setActiveTab] = useState('users'); // 'users' or 'invites'
  const [newInvite, setNewInvite] = useState({
    email: '',
    message: '',
    permissions: {
      services: false,
      invoices: false,
      billing: false,
      tickets: false,
      api: false,
      fullAccess: false
    }
  });

  // Function to get token from sessionStorage
  function getToken() {
    return sessionStorage.getItem('token');
  }

  // Fetch existing access users
  const fetchUsers = async () => {
    setLoading(true);
    setErrorMessage('');
    
    try {
      const response = await fetch("/api.php?f=get_access_users", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token: getToken() })
      });
      
      const data = await response.json();
      
      if (data.error) {
        if (data.error === 5) {
          toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
          window.location.reload(false);
        } else {
          setErrorMessage(`Error fetching users: ${data.error}`);
        }
      } else if (Array.isArray(data)) {
        const formattedUsers = data.map(user => ({
          id: user.id,
          email: user.email,
          status: user.status || 'active',
          permissions: {
            services: user.perm_services === '1' || user.perm_services === 1,
            invoices: user.perm_invoices === '1' || user.perm_invoices === 1,
            billing: user.perm_billing === '1' || user.perm_billing === 1,
            tickets: user.perm_tickets === '1' || user.perm_tickets === 1,
            api: user.perm_api === '1' || user.perm_api === 1,
            fullAccess: user.perm_full_access === '1' || user.perm_full_access === 1
          }
        }));
        setUsers(formattedUsers);
      }
    } catch (error) {
      setErrorMessage(`Failed to fetch users: ${error.message}`);
    }
  };

  // Fetch pending invitations
  const fetchPendingInvites = async () => {
    try {
      const response = await fetch("/api.php?f=get_pending_invites", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token: getToken() })
      });
      
      const data = await response.json();
      
      if (data.error) {
        if (data.error === 5) {
          toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
          window.location.reload(false);
        }
      } else if (Array.isArray(data)) {
        setPendingInvites(data);
      }
    } catch (error) {
      console.error('Failed to fetch pending invites:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchPendingInvites();
  }, []);

  // Handle permission changes for existing users
  const handlePermissionChange = async (userId, permission) => {
    try {
      const user = users.find(u => u.id === userId);
      if (!user) return;
      
      const currentValue = user.permissions[permission];
      const newValue = !currentValue;
      
      // Update UI optimistically
      setUsers(prevUsers => 
        prevUsers.map(user => {
          if (user.id === userId) {
            if (permission === 'fullAccess') {
              return {
                ...user,
                permissions: {
                  ...user.permissions,
                  fullAccess: newValue,
                  services: newValue,
                  invoices: newValue,
                  billing: newValue,
                  tickets: newValue,
                  api: newValue
                }
              };
            }
            
            const updatedPermissions = {
              ...user.permissions,
              [permission]: newValue
            };

            const allPermissionsTrue = 
              updatedPermissions.services && 
              updatedPermissions.invoices && 
              updatedPermissions.billing && 
              updatedPermissions.tickets &&
              updatedPermissions.api;

            return {
              ...user,
              permissions: {
                ...updatedPermissions,
                fullAccess: allPermissionsTrue
              }
            };
          }
          return user;
        })
      );

      const response = await fetch("/api.php?f=update_user_permission", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token: getToken(),
          user_id: userId,
          permission: permission,
          value: newValue ? 1 : 0
        })
      });
      
      const data = await response.json();
      
      if (data.error) {
        if (data.error === 5) {
          toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
          window.location.reload(false);
        } else {
          setErrorMessage(`Error updating permission: ${data.error}`);
          fetchUsers(); // Revert changes
        }
      }
    } catch (error) {
      setErrorMessage(`Failed to update permission: ${error.message}`);
      fetchUsers(); // Revert changes
    }
  };

  // Handle new invite permission changes
  const handleNewInvitePermissionChange = (permission) => {
    setNewInvite(prev => {
      if (permission === 'fullAccess') {
        return {
          ...prev,
          permissions: {
            ...prev.permissions,
            fullAccess: !prev.permissions.fullAccess,
            services: !prev.permissions.fullAccess,
            invoices: !prev.permissions.fullAccess,
            billing: !prev.permissions.fullAccess,
            tickets: !prev.permissions.fullAccess,
            api: !prev.permissions.fullAccess
          }
        };
      }
      
      const updatedPermissions = {
        ...prev.permissions,
        [permission]: !prev.permissions[permission]
      };

      const allPermissionsTrue = 
        updatedPermissions.services && 
        updatedPermissions.invoices && 
        updatedPermissions.billing && 
        updatedPermissions.tickets && 
        updatedPermissions.api;

      return {
        ...prev,
        permissions: {
          ...updatedPermissions,
          fullAccess: allPermissionsTrue
        }
      };
    });
  };

  // Send invitation
  const sendInvitation = async () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newInvite.email)) {
      toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Please enter a valid email address',
        life: 3000
      });
      return;
    }

    if (users.some(user => user.email === newInvite.email)) {
      toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'This email already has access to your account',
        life: 3000
      });
      return;
    }

    if (pendingInvites.some(invite => invite.email === newInvite.email)) {
      toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'An invitation has already been sent to this email',
        life: 3000
      });
      return;
    }
    
    try {
      const response = await fetch("/api.php?f=send_access_invite", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token: getToken(),
          email: newInvite.email,
          message: newInvite.message,
          permissions: {
            services: newInvite.permissions.services ? '1' : '0',
            invoices: newInvite.permissions.invoices ? '1' : '0',
            billing: newInvite.permissions.billing ? '1' : '0',
            tickets: newInvite.permissions.tickets ? '1' : '0',
            api: newInvite.permissions.api ? '1' : '0',
            full_access: newInvite.permissions.fullAccess ? '1' : '0'
          }
        })
      });
      
      const data = await response.json();
      
      if (data.error) {
        if (data.error === 5) {
          toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
          window.location.reload(false);
        } else {
          toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Error sending invitation: ${data.error}`,
        life: 5000
      });
        }
      } else {
        toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Invitation sent successfully!',
        life: 3000
      });
        
        // Reset form
        setNewInvite({
          email: '',
          message: '',
          permissions: {
            services: false,
            invoices: false,
            billing: false,
            tickets: false,
            api: false,
            fullAccess: false
          }
        });
        
        setShowInviteModal(false);
        fetchPendingInvites(); // Refresh pending invites
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to send invitation: ${error.message}`,
        life: 5000
      });
    }
  };

  // Cancel/Resend invitation
  const handleInviteAction = async (inviteId, action) => {
    try {
      const response = await fetch("/api.php?f=manage_invite", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token: getToken(),
          invite_id: inviteId,
          action: action // 'cancel' or 'resend'
        })
      });
      
      const data = await response.json();
      
      if (data.error) {
        if (data.error === 5) {
          toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
          window.location.reload(false);
        } else {
          toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Error: ${data.error}`,
        life: 5000
      });
        }
      } else {
        if (action === 'resend') {
          toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Invitation resent successfully!',
        life: 3000
      });
        } else {
          toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Invitation cancelled successfully!',
        life: 3000
      });
        }
        fetchPendingInvites();
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to ${action} invitation: ${error.message}`,
        life: 5000
      });
    }
  };

  // Remove existing user
  const removeUser = async (userId) => {
    if (!confirm('Are you sure you want to remove this user\'s access?')) {
      return;
    }

    try {
      const response = await fetch("/api.php?f=remove_access_user", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token: getToken(),
          user_id: userId
        })
      });
      
      const data = await response.json();
      
      if (data.error) {
        if (data.error === 5) {
          toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
          window.location.reload(false);
        } else {
          toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Error removing user: ${data.error}`,
        life: 5000
      });
        }
      } else {
        setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to remove user: ${error.message}`,
        life: 5000
      });
    }
  };

  if (loading) {
    return (
      <div className="tab-pane active">
        <div className="d-flex justify-content-center p-5">
          <div className="spinner-border text-primary" role="status"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="tab-pane active">
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h3 className="card-title">Access Management</h3>
              <button 
                className="btn btn-primary btn-sm" 
                onClick={() => setShowInviteModal(true)}
              >
                <i className="fa fa-envelope me-1"></i> Send Invitation
              </button>
            </div>
            <div className="card-body">
              {errorMessage && (
                <div className="alert alert-danger" role="alert">
                  <i className="fa fa-exclamation-circle me-2"></i>
                  {errorMessage}
                </div>
              )}
              
              {/* Tabs */}
              <ul className="nav nav-tabs" role="tablist">
                <li className="nav-item">
                  <a 
                    className={`nav-link ${activeTab === 'users' ? 'active' : ''}`}
                    onClick={() => setActiveTab('users')}
                    style={{ cursor: 'pointer' }}
                  >
                    Active Users ({users.length})
                  </a>
                </li>
                <li className="nav-item">
                  <a 
                    className={`nav-link ${activeTab === 'invites' ? 'active' : ''}`}
                    onClick={() => setActiveTab('invites')}
                    style={{ cursor: 'pointer' }}
                  >
                    Pending Invites ({pendingInvites.length})
                  </a>
                </li>
              </ul>

              {/* Active Users Tab */}
              {activeTab === 'users' && (
                <div className="table-responsive mt-3">
                  <table className="table table-bordered">
                    <thead>
                      <tr>
                        <th style={{ width: '12.5%', textAlign: 'center' }}>Email</th>
                        <th style={{ width: '12.5%', textAlign: 'center' }}>Services</th>
                        <th style={{ width: '12.5%', textAlign: 'center' }}>Invoices</th>
                        <th style={{ width: '12.5%', textAlign: 'center' }}>Billing</th>
                        <th style={{ width: '12.5%', textAlign: 'center' }}>Tickets</th>
                        <th style={{ width: '12.5%', textAlign: 'center' }}>API</th>
                        <th style={{ width: '12.5%', textAlign: 'center' }}>Full Access</th>
                        <th style={{ width: '12.5%', textAlign: 'center' }}>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map(user => (
                        <tr key={user.id}>
                          <td>{user.email}</td>
                          {['services', 'invoices', 'billing', 'tickets', 'api', 'fullAccess'].map(permission => (
                            <td key={permission} style={{ textAlign: 'center' }}>
                              <div className="form-check" style={{ display: 'inline-block', marginBottom: '1rem'}}>
                                <input
                                  type="checkbox"
                                  className="form-check-input"
                                  checked={user.permissions[permission]}
                                  onChange={() => handlePermissionChange(user.id, permission)}
                                />
                              </div>
                            </td>
                          ))}
                          <td style={{ textAlign: 'center' }}>
                            <button 
                              className="btn btn-sm btn-danger"
                              onClick={() => removeUser(user.id)}
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))}
                      {users.length === 0 && (
                        <tr>
                          <td colSpan="8" className="text-center text-muted py-4">
                            No users have access to your account yet. Send an invitation to get started.
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pending Invites Tab */}
              {activeTab === 'invites' && (
                <div className="table-responsive mt-3">
                  <table className="table table-bordered">
                    <thead>
                      <tr>
                        <th>Email</th>
                        <th>Permissions</th>
                        <th>Sent Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {pendingInvites.map(invite => (
                        <tr key={invite.id}>
                          <td>{invite.email}</td>
                          <td>
                            <div className="d-flex flex-wrap gap-1">
                              {invite.perm_services === '1' && <span className="badge badge-info">Services</span>}
                              {invite.perm_invoices === '1' && <span className="badge badge-info">Invoices</span>}
                              {invite.perm_billing === '1' && <span className="badge badge-info">Billing</span>}
                              {invite.perm_tickets === '1' && <span className="badge badge-info">Tickets</span>}
                              {invite.perm_api === '1' && <span className="badge badge-info">API</span>}
                              {invite.perm_full_access === '1' && <span className="badge badge-warning">Full Access</span>}
                            </div>
                          </td>
                          <td>{new Date(invite.created_at).toLocaleDateString()}</td>
                          <td>
                            <span className={`badge ${invite.status === 'pending' ? 'badge-warning' : 'badge-secondary'}`}>
                              {invite.status}
                            </span>
                          </td>
                          <td>
                            <div className="btn-group btn-group-sm">
                              <button 
                                className="btn btn-outline-primary"
                                onClick={() => handleInviteAction(invite.id, 'resend')}
                                title="Resend Invitation"
                              >
                                <i className="fa fa-paper-plane"></i>
                              </button>
                              <button 
                                className="btn btn-outline-danger"
                                onClick={() => handleInviteAction(invite.id, 'cancel')}
                                title="Cancel Invitation"
                              >
                                <i className="fa fa-times"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                      {pendingInvites.length === 0 && (
                        <tr>
                          <td colSpan="5" className="text-center text-muted py-4">
                            No pending invitations.
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Send Invitation Modal */}
      {showInviteModal && (
        <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
          <div className="modal-dialog modal-lg" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Send Access Invitation</h5>
                <button type="button" className="close" onClick={() => setShowInviteModal(false)}>
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div className="modal-body">
                <div className="form-group mb-3">
                  <label>Email Address</label>
                  <input
                    type="email"
                    className="form-control"
                    placeholder="Enter email address"
                    value={newInvite.email}
                    onChange={(e) => setNewInvite(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>

                <div className="form-group mb-3">
                  <label>Personal Message (Optional)</label>
                  <textarea
                    className="form-control"
                    rows="3"
                    placeholder="Add a personal message to the invitation..."
                    value={newInvite.message}
                    onChange={(e) => setNewInvite(prev => ({ ...prev, message: e.target.value }))}
                  />
                </div>

                <div className="form-group">
                  <label>Access Permissions</label>
                  <div className="row">
                    <div className="col-6">
                      {['services', 'invoices', 'api'].map(permission => (
                        <div key={permission} className="form-check">
                          <input
                            type="checkbox"
                            className="form-check-input"
                            id={`newInvite${permission}`}
                            checked={newInvite.permissions[permission]}
                            onChange={() => handleNewInvitePermissionChange(permission)}
                          />
                          <label className="form-check-label" htmlFor={`newInvite${permission}`}>
                            {permission.charAt(0).toUpperCase() + permission.slice(1)}
                          </label>
                        </div>
                      ))}
                    </div>
                    <div className="col-6">
                      {['billing', 'tickets'].map(permission => (
                        <div key={permission} className="form-check">
                          <input
                            type="checkbox"
                            className="form-check-input"
                            id={`newInvite${permission}`}
                            checked={newInvite.permissions[permission]}
                            onChange={() => handleNewInvitePermissionChange(permission)}
                          />
                          <label className="form-check-label" htmlFor={`newInvite${permission}`}>
                            {permission.charAt(0).toUpperCase() + permission.slice(1)}
                          </label>
                        </div>
                      ))}
                      <div className="form-check">
                        <input
                          type="checkbox"
                          className="form-check-input"
                          id="newInviteFullAccess"
                          checked={newInvite.permissions.fullAccess}
                          onChange={() => handleNewInvitePermissionChange('fullAccess')}
                        />
                        <label className="form-check-label" htmlFor="newInviteFullAccess">
                          <strong>Full Access</strong>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary" 
                  onClick={() => setShowInviteModal(false)}
                >
                  Cancel
                </button>
                <button 
                  type="button" 
                  className="btn btn-primary"
                  onClick={sendInvitation}
                  disabled={!newInvite.email}
                >
                  <i className="fa fa-envelope me-1"></i> Send Invitation
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};




  
export default Account