import React, { useEffect, useState } from "react"
import { Outlet, Link, useParams, useNavigate, useLocation } from "react-router-dom";
import { Toast } from 'primereact/toast';
import Tab from 'react-bootstrap/Tab';
import Tabs from 'react-bootstrap/Tabs';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';


const Manage = () => {
  const toast = React.useRef(null);
  const params = useParams();
  const [server, setServer] = useState([])
  const [upgradeableDisks, setUpgradeableDisks] = useState([])
  const [upgradeableBandwidth, setUpgradeableBandwidth] = useState([])
  const [upgradeableSubnets, setUpgradeableSubnets] = useState([])
  const [reinstallableOS, setReinstallableOS] = useState([])
  const path = useLocation();
  const navigate = useNavigate();
  const first = 1

  function getToken() {
    const userToken = sessionStorage.getItem('token');
    return userToken
  }
  const token = getToken();
  function fetchServersData(input) {
    fetch("/New_client/api.php?f=manage_dedicated&id="+params.id, {
      	method: 'POST',
      	headers: {
        	'Content-Type': 'application/json'
       	},
      		body: JSON.stringify(input)
      	})
      .then(response => {
        return response.json()
      })
      .then(data => {
	if(data.error)
		if(data.error == 5){
			toast.current.show({
				severity: 'error',
				summary: 'Session Timeout',
				detail: 'Your login session has timed out',
				life: 5000
			});
			window.location.reload(false);
		}else
			toast.current.show({
				severity: 'error',
				summary: 'Error',
				detail: 'ERROR: '+data.error,
				life: 5000
			});
	else
        setServer(data)
      })
  }
  useEffect(() => {
    fetchServersData({token})
  }, [])

  function fetchDiskUpgradesData(input) {
    fetch("/New_client/api.php?f=upgradeable_disk_layouts&id="+params.id, {
      	method: 'POST',
      	headers: {
        	'Content-Type': 'application/json'
       	},
      		body: JSON.stringify(input)
      	})
      .then(response => {
        return response.json()
      })
      .then(data => {
	if(data.error)
		if(data.error == 5){
			alert('ERROR: Your login session has timed out')
			window.location.reload(false);
		}else
			alert('ERROR: '+data.error)
	else
        setUpgradeableDisks(data)
      })
  }
  useEffect(() => {
    fetchDiskUpgradesData({token})
  }, [])

  function fetchBandwidthUpgradesData(input) {
    fetch("/New_client/api.php?f=upgradeable_bandwidth&id="+params.id, {
      	method: 'POST',
      	headers: {
        	'Content-Type': 'application/json'
       	},
      		body: JSON.stringify(input)
      	})
      .then(response => {
        return response.json()
      })
      .then(data => {
	if(data.error)
		if(data.error == 5){
			alert('ERROR: Your login session has timed out')
			window.location.reload(false);
		}else
			alert('ERROR: '+data.error)
	else
        setUpgradeableBandwidth(data)
      })
  }
  useEffect(() => {
    fetchBandwidthUpgradesData({token})
  }, [])

  function fetchSubnetUpgradesData(input) {
    fetch("/New_client/api.php?f=upgradeable_subnets&id="+params.id, {
      	method: 'POST',
      	headers: {
        	'Content-Type': 'application/json'
       	},
      		body: JSON.stringify(input)
      	})
      .then(response => {
        return response.json()
      })
      .then(data => {
	if(data.error)
		if(data.error == 5){
			alert('ERROR: Your login session has timed out')
			window.location.reload(false);
		}else
			alert('ERROR: '+data.error)
	else
        setUpgradeableSubnets(data)
      })
  }
  useEffect(() => {
    fetchSubnetUpgradesData({token})
  }, [])

  function fetchReinstallableOSData(input) {
    fetch("/New_client/api.php?f=reinstallable_os&id="+params.id, {
      	method: 'POST',
      	headers: {
        	'Content-Type': 'application/json'
       	},
      		body: JSON.stringify(input)
      	})
      .then(response => {
        return response.json()
      })
      .then(data => {
	if(data.error)
		if(data.error == 5){
			alert('ERROR: Your login session has timed out')
			window.location.reload(false);
		}else
			alert('ERROR: '+data.error)
	else
        setReinstallableOS(data)
      })
  }
  useEffect(() => {
    fetchReinstallableOSData({token})
  }, [])


useEffect(() => {
  const interval = setInterval(() => {
    fetchServersData({token})
  }, 10000);
  return () => clearInterval(interval);
}, []);


  const [basicModal, setBasicModal] = useState(false);
  const toggleShow = () => setBasicModal(!basicModal);

  const routeChange = () =>{ 
    navigate('configuration');
  }
  const routeChangeBilling = () =>{ 
    navigate('billing');
  }


  const RenewTitle = <>Renew</>
  const RenewBody = <><div class="form-group">
			Payment Method:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				<option value="cc">Credit Card</option>
				<option value="cr">Cryptocurrency</option>
				<option value="bt">Bank Transfer</option>
			</select><br/>
			Period:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				<option value="cc">1 Month</option>
				<option value="cr">3 Months (-5% discount)</option>
				<option value="bt">6 Months (-7% discount)</option>
				<option value="bt">12 Months (-10% discount)</option>
			</select>
		</div></>
  const RenewFooter = <><button class="btn btn-success" onClick={toggleShow}>Renew</button></>


  const CancelTitle = <>Cancel</>
  const CancelBody = <><div class="form-group">
			Cancellation Date:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				<option value="cc">Immediately</option>
				<option value="cr">At the end of the current billing cycle</option>
			</select><br/><font color="red"><b>Warning: All data from your server will be deleted. If you cancel your server immediately, no credit or refund is granted for the remaining paid in advance period</b></font> 
		</div></>
  const CancelFooter = <><button class="btn btn-danger" onClick={toggleShow}>Cancel</button></>


  const PrefTitle = <>Preferences</>
  const PrefBody = <><div class="form-group">
			Default Method:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				<option value="cc">Credit Card</option>
				<option value="cr">Cryptocurrency</option>
				<option value="cr">Bank Transfer</option>
			</select><br/>
			Billing Cycle:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				<option value="cc">Monthly</option>
				<option value="cr">Quarterly</option>
				<option value="cr">Semi-Annual</option>
				<option value="cr">Yearly</option>
			</select><br/>
			Auto Payments:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				<option value="cc">Enabled</option>
				<option value="cr">Disabled</option>
			</select><br/>
			Auto-apply Credit:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				<option value="cc">Enabled</option>
				<option value="cr">Disabled</option>
			</select><br/><font color="red"><b>Warning: Disabling the automatic payments or automatic credit assignment may lead to unexpected termination of your server if you accidentally forget to pay before the expiration date</b></font>
		</div></>
  const PrefFooter = <><button class="btn btn-success" onClick={toggleShow}>Save</button></>


  const RebootTitle = <>Reboot</>
  const RebootBody = <><div class="form-group">
			After rebooting, please allow up to 10 minutes for the server to become accessible on SSH. If you can't access your server after 10 minutes, please use the OOB console to debug it remotely.<br/><br/><font color="red"><b>WARNING: This action leads to downtime</b></font>
		</div></>
  const RebootFooter = <><button class="btn btn-warning" onClick={toggleShow}>Reboot</button></>


  const ReinstallTitle = <>Reinstall</>
  const ReinstallBody = <><div class="form-group">
			Select Operating System:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				{reinstallableOS.map(reinstallableOS => (<><option value="{reinstallableOS.id}">{reinstallableOS.name}</option></>))}			</select><br/>After initiating the reinstallation procedure, the remote power control and reinstallation functions are disabled to avoid interrupting the process. You will receive an email and a notification in the client area when the reinstallation is complete.<br/><br/>
			<font color="red"><b>WARNING: This function will delete all existing data from your server and leads to 10-20 min downtime</b></font>
		</div></>
  const ReinstallFooter = <><button class="btn btn-danger" onClick={toggleShow}>Reinstall</button></>


  const DisksTitle = <>Disks Configuration</>
  const DisksBody = <><div class="form-group">
			Select new disks layout:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				{upgradeableDisks.map(upgradeableDisks => (<><option value="{upgradeableDisks.id}">{upgradeableDisks.name} ({(upgradeableDisks.price >= 0) && "+\u20AC"+upgradeableDisks.price || "-\u20AC"+Math.abs(upgradeableDisks.price)}/mo)</option></>))}
			</select><br/>Disk layout changes are processed in 1-7 days, depending on availability<br/><br/>
			<font color="red"><b>WARNING: The data from the currently installed disks will be securely deleted. Make sure that you take a backup of your data first</b></font>
		</div></>
  const DisksFooter = <><button class="btn btn-success" onClick={toggleShow}>Order</button></>


  const IpsTitle = <>IPv4 Configuration</>
  const IpsBody = <><div class="form-group">
			Select Subnet:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				{upgradeableSubnets.map(upgradeableSubnets => (<><option value="{upgradeableSubnets.id}">{upgradeableSubnets.name} ({(upgradeableSubnets.is_recurring == 0) && "\u20AC"+upgradeableSubnets.price+" one time" || (upgradeableSubnets.price >= 0) && "+\u20AC"+upgradeableSubnets.price+"/mo" || "-\u20AC"+Math.abs(upgradeableSubnets.price)+"/mo"})</option></>))}
			</select><br/>Subnet replacements are processed automatically. Please make sure to configure the new subnet on your server by using the OOB console available in your client area or open a smart ticket to have it configured by our engineers<br/><br/>
			<font color="red"><b>WARNING: This function makes your server inaccessible.</b></font>
		</div></>
  const IpsFooter = <><button class="btn btn-success" onClick={toggleShow}>Order</button></>


  const NetworkTitle = <>Network Configuration</>
  const NetworkBody = <><div class="form-group">
			Select Bandwidth Plan:
			<select name="country" id="select-countries" class="form-control custom-select select2 select2-hidden-accessible" tabindex="-1" aria-hidden="true">
				{upgradeableBandwidth.map(upgradeableBandwidth => (<><option value="{upgradeableBandwidth.id}">{upgradeableBandwidth.name} ({(upgradeableBandwidth.price >= 0) && "+\u20AC"+upgradeableBandwidth.price || "-\u20AC"+Math.abs(upgradeableBandwidth.price)}/mo)</option></>))}
			</select><br/>Changes are processed automatically. Your new bandwidth plan will take effect in up to 10 minutes. No downtime is expected.
		</div></>
  const NetworkFooter = <><button class="btn btn-success" onClick={toggleShow}>Order</button></>



  const [modalTitle, setModalTitle] = useState('Click');
  function handleModalTitle(title) {
    setModalTitle(title);
  }

  const [modalBody, setModalBody] = useState('Click');
  function handleModalBody(body) {
    setModalBody(body);
  }

  const [modalFooter, setModalFooter] = useState('Click');
  function handleModalFooter(footer) {
    setModalFooter(footer);
  }


function splitLastOccurrence(str, substring) {
  const lastIndex = str.lastIndexOf(substring);
  const before = str.slice(0, lastIndex);
  const after = str.slice(lastIndex + 1);
  return after;
}

function openConsole(ipmi_type,ipmi_ip,ipmi_user,ipmi_pass) {

    fetch("https://"+ipmi_ip+"/data/login", {
      	method: 'POST',
        headers: new Headers({
            'Accept': 'application/xml',
            'content-type': 'application/x-www-form-urlencoded',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT',
        }),
      	body: "user="+ipmi_user+"&password="+ipmi_pass
      	})
    .then(response => response.text())
    .then(text => console.log(text));
}


  return 				<><div class="page-header">
						<div class="page-leftheader">
						<ol class="breadcrumb">
							<li class="breadcrumb-item1"><Link to="/">Services</Link></li>
							<li class="breadcrumb-item1">Manage</li>
						</ol>
						</div>
						<div class="page-rightheader ml-auto">
							<div class="dropdown">
										<a href="#" class="nav-link pr-0 leading-none" data-toggle="dropdown">
												<button class="btn btn-success">New Service</button>
										</a>
										<div class="dropdown-menu">
											<Link class="dropdown-item d-flex" to="/dedicatedorder">
												<div class="mt-1">Dedicated Server</div>
											</Link>
											<Link class="dropdown-item d-flex" to="/cloudorder">
												<div class="mt-1">Cloud Server</div>
											</Link>
											<Link class="dropdown-item d-flex" to="/colocationorder">
												<div class="mt-1">Colocation</div>
											</Link>
											<Link class="dropdown-item d-flex" to="/colocationassetsorder">
												<div class="mt-1">Colocation Assets</div>
											</Link>
											<Link class="dropdown-item d-flex" to="/iptransitorder">
												<div class="mt-1">IP Transit</div>
											</Link>
										</div>
							</div>
						</div>
					</div>
					<div class="row">

							<div class="col-12">

								<div class="card">
									<div class="card-header">
										<div class="card-title">Server #{params.id} {server.map(server => (<>{(server.server_status == 'Online') && <span class="badge bg-success">Online</span>}{(server.server_status != 'Online') && <span class="badge bg-danger">{server.server_status}</span>}</>))}</div>
										<div class="card-options">
											<a href="javascript:void(0)" class="btn btn-warning btn-sm" onClick={() => {handleModalTitle(RebootTitle);handleModalBody(RebootBody);handleModalFooter(RebootFooter);toggleShow()}}>Reboot</a>&nbsp;
											<a href="javascript:void(0)" class="btn btn-default btn-sm ms-2" onClick={() => server.map(server => openConsole(server.ipmi_type,server.ipmi_ip,server.ipmi_user,server.ipmi_pass))}>Console</a>&nbsp;
											<a href="javascript:void(0)" class="btn btn-danger btn-sm ms-2" onClick={() => {handleModalTitle(ReinstallTitle);handleModalBody(ReinstallBody);handleModalFooter(ReinstallFooter);toggleShow()}}>Reinstall</a>
										</div>

									</div>
									<div class="card-body">
											<div class="panel panel-primary">
											<div class="tab-menu-heading">
												<div class="tabs-menu ">
													<ul class="nav panel-tabs">
														<li><Link to="status" className={(splitLastOccurrence(path.pathname, '/') == 'status') ? "active" : ""} data-bs-toggle="tab">Manage</Link></li>
														<li><Link to="graphs" className={(splitLastOccurrence(path.pathname, '/') == 'graphs') ? "active" : ""} data-bs-toggle="tab">Graphs</Link></li>
														<li><Link to="billing" className={(splitLastOccurrence(path.pathname, '/') == 'billing') ? "active" : ""} data-bs-toggle="tab">Billing</Link></li>
													</ul>
												</div>
											</div>
											<div class="panel-body tabs-menu-body">
												<div class="tab-content">
													{(splitLastOccurrence(path.pathname, '/') == 'status' || '') && (
													<div class="tab-pane active">
														<div class="row">
														<div class="col-xl-6 col-md-12">
														<div class="card">
														<div class="card-header">
															<h3 class="card-title">Configuration</h3>
														</div>
														<div class="card-body">
														<ul class="list-group">
															<li class="list-group-item"><b>CPU:</b> {server.map(server => (<>{server.cpu}</>))}</li>
															<li class="list-group-item"><b>RAM:</b> {server.map(server => (<>{server.ram}</>))}</li>
															<li class="list-group-item"><b>Disks:</b> {server.map(server => (<>{server.disks}</>))}<span class={"float-right"}><button class="btn btn-primary btn-sm" onClick={() => {handleModalTitle(DisksTitle);handleModalBody(DisksBody);handleModalFooter(DisksFooter);toggleShow()}}>Change</button></span></li>
															<li class="list-group-item"><b>Subnet:</b> {server.map(server => (<>{server.subnet}</>))}<span class={"float-right"}><button class="btn btn-primary btn-sm" onClick={() => {handleModalTitle(IpsTitle);handleModalBody(IpsBody);handleModalFooter(IpsFooter);toggleShow()}}>Change</button></span></li>
															<li class="list-group-item"><b>Current OS:</b> &nbsp;{server.map(server => (<><img style={{width: "20px", verticalAlign: "bottom"}} src={server.os_logo_url}/></>))} &nbsp;{server.map(server => (<>{server.os}</>))}<span class={"float-right"}><button class="btn btn-primary btn-sm" onClick={() => {handleModalTitle(ReinstallTitle);handleModalBody(ReinstallBody);handleModalFooter(ReinstallFooter);toggleShow()}}>Change</button></span></li>
															<li class="list-group-item"><b>Bandwidth:</b> {server.map(server => (<>{server.bandwidth}</>))}<span class={"float-right"}><button class="btn btn-primary btn-sm" onClick={() => {handleModalTitle(NetworkTitle);handleModalBody(NetworkBody);handleModalFooter(NetworkFooter);toggleShow()}}>Change</button></span></li>
														</ul>
														</div>
														</div>
														</div>
														<div class="col-xl-6 col-md-12">
														<div class="card">
														<div class="card-header">
															<h3 class="card-title">Status</h3>
														</div>
														<div class="card-body">
														<ul class="list-group">
															<li class="list-group-item"><b>Power:</b> {server.map(server => (<>{(server.server_status == 'Online') && <font color="green"><b>Online</b></font>}{(server.server_status != 'Online') && <font color="red"><b>{server.server_status}</b></font>}</>))}</li>
															<li class="list-group-item"><b>Switch Port:</b> {server.map(server => (<>{(server.switchport == 'Connected') && <font color="green"><b>Connected</b></font>}{(server.switchport != 'Connected') && <font color="red"><b>{server.switchport}</b></font>}</>))}</li>
															<li class="list-group-item" onClick={routeChangeBilling}><b>Expires:</b> {server.map(server => (<>{server.expires}</>))}</li>
															<li class="list-group-item"><b>Location:</b> {server.map(server => (<><i className={'flag flag-'+server.location}></i> &nbsp;{server.locationname}</>))}</li>
															<li class="list-group-item"><b>Datacenter:</b> {server.map(server => (<>{server.datacenter}</>))}</li>
															<li class="list-group-item"><b>Cabinet:</b> {server.map(server => (<>{server.cabinet}</>))}</li>
														</ul>
														</div>
														</div>
														</div>
													</div>

													</div>
													)}
													{(splitLastOccurrence(path.pathname, '/') == 'graphs' || '') && (
													<div class="tab-pane active " id="tab1">
														<div class="row">
														<div class="col-xl-3 col-md-12">
															<div class="card">
																<div class="card-header">
																	<h3 class="card-title">Bandwidth (24h)</h3>
																</div>
																<div class="card-body">
																	<img src="https://www.researchgate.net/profile/Freek-Dijkstra/publication/267769296/figure/fig4/AS:295723942858758@1447517490165/Bandwidth-graph-showing-the-rate-at-the-output-port-of-the-switch-in-Amsterdam-on.png"/>
																</div>
															</div>
														</div>
														<div class="col-xl-3 col-md-12">

															<div class="card">
																<div class="card-header">
																	<h3 class="card-title">Uptime (24h)</h3>
																</div>
																<div class="card-body">
																	<img src="https://www.researchgate.net/profile/Freek-Dijkstra/publication/267769296/figure/fig4/AS:295723942858758@1447517490165/Bandwidth-graph-showing-the-rate-at-the-output-port-of-the-switch-in-Amsterdam-on.png"/>
																</div>
															</div>
														</div>
														</div>
													</div>
													)}
													{(splitLastOccurrence(path.pathname, '/') == 'billing' || '') && (
													<div class="tab-pane active " id="tab1">
														<div class="row">
														<div class="col-xl-3 col-md-12">
															<div class="card">
																<div class="card-header">
																	<h3 class="card-title">Renewal</h3>
																</div>
																<div class="card-body">
																	<ul class="list-group">
																		<li class="list-group-item"><b>Activated On:</b> January 25 2023</li>
																		<li class="list-group-item"><b>Next Renewal:</b> May 25 2023</li>
																		<li class="list-group-item"><b>Recurring Price:</b> &euro;519</li>
																		<li class="list-group-item"><b>Discount Rate:</b> 0%</li>
																	</ul>
																</div>
																<div class="card-footer">
																	<button class="btn btn-success btn-sm" onClick={() => {handleModalTitle(RenewTitle);handleModalBody(RenewBody);handleModalFooter(RenewFooter);toggleShow()}}>Renew</button>&nbsp;<button class="btn btn-danger btn-sm" onClick={() => {handleModalTitle(CancelTitle);handleModalBody(CancelBody);handleModalFooter(CancelFooter);toggleShow()}}>Cancel</button>
																</div>

															</div>
														</div>

        													<div class="col-xl-3 col-md-12">
															<div class="card">
																<div class="card-header">
																	<h3 class="card-title">Preferences</h3>
																</div>
																<div class="card-body">
																	<ul class="list-group">
																		<li class="list-group-item"><b>Default Method:</b> Credit Card</li>
																		<li class="list-group-item"><b>Billing Cycle:</b> Monthly</li>
																		<li class="list-group-item"><b>Auto Payments:</b> <font color="green"><b>Enabled</b></font></li>
																		<li class="list-group-item"><b>Auto Credit:</b> <font color="green"><b>Enabled</b></font></li>
																	</ul>
																</div>
																<div class="card-footer">
																	<button class="btn btn-primary btn-sm" onClick={() => {handleModalTitle(PrefTitle);handleModalBody(PrefBody);handleModalFooter(PrefFooter);toggleShow()}}>Change</button>
																</div>

															</div>
														</div>

														</div>
													</div>
													)}

												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
					</div>
<MDBModal show={basicModal} setShow={setBasicModal} tabIndex='-1'>
        <MDBModalDialog>
          <MDBModalContent>
            <MDBModalHeader>
              <MDBModalTitle>{modalTitle}</MDBModalTitle>
              <button class="close" onClick={toggleShow}><span aria-hidden="true">x</span></button>
            </MDBModalHeader>
            <MDBModalBody>
		{modalBody}
	    </MDBModalBody>
            <MDBModalFooter>
              {modalFooter}
            </MDBModalFooter>
          </MDBModalContent>
        </MDBModalDialog>
      </MDBModal>
</>
;
};

export default Manage; 