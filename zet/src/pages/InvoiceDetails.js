import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { Link, useParams, useLocation } from "react-router-dom";
import { Toast } from 'primereact/toast';
import PaymentModal from './PaymentModal';

const InvoiceDetails = () => {
  const { id } = useParams();
  const location = useLocation();
  const toast = useRef(null);
  const printRef = useRef();
  const processedPayPalParams = useRef(new Set()); // Track processed PayPal parameters
  
  // Consolidated state management
  const [data, setData] = useState({
    invoice: null,
    transactionLogs: [],
    accountCredit: 0,
    userDetails: null,
    vatInfo: null
  });
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionsState, setActionsState] = useState({
    applyingCredit: false,
    applyingVoucher: false
  });
  
  // Payment modal state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  
  // Modal state
  const [modals, setModals] = useState({
    showCreditModal: false,
    showVoucherModal: false,
    applyFullCredit: true,
    customCreditAmount: 0,
    voucherCode: ''
  });

  // Payment verification state
  const [paymentStatus, setPaymentStatus] = useState('idle');
  const [statusMessage, setStatusMessage] = useState('');

  // Helper function to get token from session storage
  const getToken = useCallback(() => {
    return sessionStorage.getItem('token');
  }, []);

  // Consolidated API call
  const fetchAllInvoiceData = useCallback(async (showLoader = true) => {
    if (showLoader) {
      setLoading(true);
    }
    setError(null);

    try {
      const token = getToken();
      
      // Using Promise.all to fetch data in parallel
      const [invoiceResponse, creditResponse] = await Promise.all([
        fetch(`/api.php?f=invoice_details&id=${id}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token })
        }),
        fetch("/api.php?f=get_available_credit", {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token })
        })
      ]);

      // Handle invoice response
      if (!invoiceResponse.ok) {
        throw new Error(`HTTP error! Status: ${invoiceResponse.status}`);
      }

      const invoiceData = await invoiceResponse.json();
      if (invoiceData.error) {
        if (invoiceData.error === 5) {
          toast.current.show({
        severity: 'error',
        summary: 'Session Timeout',
        detail: 'Your login session has timed out',
        life: 5000
      });
          window.location.reload(false);
          return;
        }
        throw new Error(`Error fetching invoice: ${invoiceData.error}`);
      }

      // Process VAT rate
      let taxRate = null;
      if (invoiceData.items) {
        const vatItem = invoiceData.items.find(item =>
          item.description && item.description.toLowerCase().includes('vat')
        );
        if (vatItem) {
          const match = vatItem.description.match(/\((\d+)%\)/);
          if (match && match[1]) {
            taxRate = match[1];
          }
        }
      }

      if (!taxRate && invoiceData.subtotal > 0 && invoiceData.tax > 0) {
        taxRate = ((invoiceData.tax / invoiceData.subtotal) * 100).toFixed(0);
      }

      // Process credit data
      const creditData = await creditResponse.json();
      const availableCredit = !creditData.error ? 
        parseFloat(creditData.available_credit || 0) + parseFloat(creditData.free_credit || 0) : 0;
      
      // Additional parallel requests for logs and VAT
      const [logsResponse, vatResponse] = await Promise.all([
        fetch(`/api.php?f=invoice_transactions&id=${id}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token })
        }),
        fetch("/api.php?f=get_vat_rate", {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token })
        })
      ]);

      // Process logs
      let logs = [];
      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        if (!logsData.error) {
          logs = Array.isArray(logsData) ? logsData : [];
        }
      }

      // Process VAT info
      let vatInfo = null;
      if (vatResponse.ok) {
        const vatData = await vatResponse.json();
        if (vatData.success) {
          vatInfo = vatData;
        }
      }

      // Update all data at once to reduce renders
      setData({
        invoice: {
          ...invoiceData,
          taxRate: taxRate
        },
        transactionLogs: logs,
        accountCredit: availableCredit,
        userDetails: invoiceData.billingInfo || null,
        vatInfo: vatInfo
      });

    } catch (err) {
      console.error("Error fetching data:", err);
      setError(err.message);
      if (toast.current) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: `Failed to load invoice details: ${err.message}`,
          life: 5000
        });
      }
    } finally {
      if (showLoader) {
        setLoading(false);
      }
    }
  }, [id, getToken]);

  // FIXED: PayPal verification function - no loops, clean state management
  const verifyPayPalPayment = useCallback(async (paymentId, payerId, invoiceId) => {
    try {
      console.log(`Starting PayPal verification for payment: ID=${paymentId}, PayerID=${payerId}, Invoice=${invoiceId}`);
      
      // Validate we have all required parameters
      if (!paymentId || !payerId || !invoiceId) {
        console.error("Missing required parameters for PayPal verification");
        throw new Error("Missing required parameters for payment verification");
      }
      
      // Check if we're already in a success state - prevent duplicate verification
      if (paymentStatus === 'success') {
        console.log('Payment already successful, skipping verification');
        return;
      }
      
      setPaymentStatus('verifying');
      setStatusMessage('Verifying your PayPal payment...');
      
      const token = getToken();
      if (!token) {
        console.error('Authentication token not found for PayPal verification');
        throw new Error('Authentication token not found');
      }

      // Use absolute URL path to ensure correct routing
      const apiUrl = "/api.php?f=verify_paypal_payment";
      console.log(`Making PayPal verification request to: ${apiUrl}`);

      // Create the request payload
      const payload = { 
        token, 
        payment_id: paymentId,
        payer_id: payerId,
        invoice_id: invoiceId 
      };
      
      console.log("PayPal verification payload:", payload);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      // Log the raw response status for debugging
      console.log("PayPal verification response status:", response.status);
      
      // If response is not ok, try to read the response text for error details
      if (!response.ok) {
        const errorText = await response.text();
        console.error("PayPal verification error response:", errorText);
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }

      // Read the raw response text
      const rawResponse = await response.text();
      console.log("Raw PayPal verification response:", rawResponse);
      
      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(rawResponse);
        console.log("Parsed PayPal verification response:", data);
      } catch (e) {
        console.error("Error parsing PayPal verification JSON:", e);
        throw new Error(`Invalid JSON response: ${rawResponse.substring(0, 100)}`);
      }
      
      if (data.success && data.is_paid) {
        console.log("PayPal payment verified successfully!");
        
        // Clear localStorage immediately
        localStorage.removeItem('paypal_payment_info');
        
        // Set success status
        setPaymentStatus('success');
        setStatusMessage(data.message || 'Payment completed successfully! Your invoice has been paid.');
        
        // Refresh invoice data to show updated status
        await fetchAllInvoiceData(false);
        
        console.log("PayPal verification completed successfully - status should now be success");
        
        // CRITICAL: Return here to prevent further processing
        return;
        
      } else if (data.success && !data.is_paid && (data.payment_status === 'pending' || data.status === 'pending')) {
        // Payment is still processing - NO RETRY to prevent loops
        console.log("PayPal payment is still pending");
        setPaymentStatus('verifying');
        setStatusMessage('Payment is being processed. Please wait...');
        
      } else if (!data.success) {
        console.error("PayPal verification failed:", data.error || "Unknown error");
        setPaymentStatus('failed');
        
        // Provide more specific error messages
        let errorMessage = data.error || 'Payment verification failed.';
        if (errorMessage.includes('ORDER_ALREADY_CAPTURED')) {
          errorMessage = 'Payment has already been processed. Refreshing invoice status...';
          // Refresh the invoice to show updated status
          setTimeout(() => {
            fetchAllInvoiceData(false);
            setPaymentStatus('success');
            setStatusMessage('Payment has been processed successfully.');
          }, 2000);
        } else if (errorMessage.includes('INVALID_PAYMENT_ID')) {
          errorMessage = 'Invalid payment ID. Please try again or contact support.';
        }
        
        setStatusMessage(errorMessage);
        
      } else {
        console.error("PayPal verification returned unexpected response:", data);
        setPaymentStatus('failed');
        setStatusMessage('Payment verification failed. Please contact support if your payment was processed.');
      }
    } catch (err) {
      console.error("PayPal payment verification error:", err);
      setPaymentStatus('failed');
      setStatusMessage('Error verifying PayPal payment. Please refresh the page or contact support.');
    }
  }, [getToken, fetchAllInvoiceData, paymentStatus]);

  // Define verifyStripePayment with useCallback to avoid initialization issues
  const verifyStripePayment = useCallback(async (sessionId, invoiceId) => {
    try {
      console.log(`Verifying payment for session ${sessionId} and invoice ${invoiceId}`);
      setPaymentStatus('verifying');
      setStatusMessage('Verifying your payment...');
      
      const token = getToken();
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Use absolute URL path to ensure correct routing
      const apiUrl = "/api.php?f=verify_stripe_payment";
      console.log(`Making verification request to: ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token, 
          session_id: sessionId,
          invoice_id: invoiceId 
        })
      });

      // Log the raw response status for debugging
      console.log("Response status:", response.status);
      
      // If response is not ok, try to read the response text for error details
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response text:", errorText);
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }

      // Try to parse the response as JSON
      let data;
      try {
        const rawResponse = await response.text();
        console.log("Raw verification response:", rawResponse);
        
        try {
          data = JSON.parse(rawResponse);
        } catch (e) {
          console.error("Error parsing JSON:", e);
          throw new Error(`Invalid JSON response: ${rawResponse.substring(0, 100)}`);
        }
      } catch (e) {
        console.error("Error reading response:", e);
        throw new Error(`Error reading response: ${e.message}`);
      }
      
      console.log("Parsed verification response:", data);
      
      if (data.success && data.is_paid) {
        setPaymentStatus('success');
        setStatusMessage(data.message || 'Payment completed successfully! Your invoice has been paid.');
        
        // Refresh invoice data to show updated status
        fetchAllInvoiceData(false);
      } else if (data.success && data.session_status === 'pending') {
        // Payment is still processing, check again after a delay
        setTimeout(() => verifyStripePayment(sessionId, invoiceId), 3000);
      } else if (!data.success) {
        setPaymentStatus('failed');
        setStatusMessage(data.error || 'Payment verification failed. Please contact support.');
      } else {
        setPaymentStatus('failed');
        setStatusMessage('Payment verification failed. Please contact support if your payment was processed.');
      }
    } catch (err) {
      console.error("Payment verification error:", err);
      setPaymentStatus('failed');
      setStatusMessage('Error verifying payment. Please refresh the page or contact support.');
    }
  }, [getToken, fetchAllInvoiceData]);


  // CoinGate verification function
  const verifyCoinGatePayment = useCallback(async (cgToken, invoiceId) => {
    try {
      console.log(`Starting CoinGate verification with token: ${cgToken}, Invoice=${invoiceId}`);
      
      // Validate we have all required parameters
      if (!cgToken || !invoiceId) {
        console.error("Missing required parameters for CoinGate verification");
        throw new Error("Missing required parameters for payment verification");
      }
      
      // Check if we're already in a success state - prevent duplicate verification
      if (paymentStatus === 'success') {
        console.log('Payment already successful, skipping verification');
        return;
      }
      
      setPaymentStatus('verifying');
      setStatusMessage('Verifying your cryptocurrency payment...');
      
      const token = getToken();
      if (!token) {
        console.error('Authentication token not found for CoinGate verification');
        throw new Error('Authentication token not found');
      }
  
      // Use absolute URL path to ensure correct routing
      const apiUrl = "/api.php?f=verify_coingate_payment";
      console.log(`Making CoinGate verification request to: ${apiUrl}`);
  
      // Create the request payload - UPDATED to use tracking token
      const payload = { 
        token, 
        cg_token: cgToken,  // Use tracking token instead of direct coingate_id
        invoice_id: invoiceId 
      };
      
      console.log("CoinGate verification payload:", payload);
  
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
  
      // Log the raw response status for debugging
      console.log("CoinGate verification response status:", response.status);
      
      // If response is not ok, try to read the response text for error details
      if (!response.ok) {
        const errorText = await response.text();
        console.error("CoinGate verification error response:", errorText);
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }
  
      // Read the raw response text
      const rawResponse = await response.text();
      console.log("Raw CoinGate verification response:", rawResponse);
      
      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(rawResponse);
        console.log("Parsed CoinGate verification response:", data);
      } catch (e) {
        console.error("Error parsing CoinGate verification JSON:", e);
        throw new Error(`Invalid JSON response: ${rawResponse.substring(0, 100)}`);
      }
      
      if (data.success && data.is_paid) {
        console.log("CoinGate payment verified successfully!");
        
        // Clear localStorage immediately
        localStorage.removeItem('coingate_payment_info');
        
        // Set success status
        setPaymentStatus('success');
        setStatusMessage(data.message || 'Cryptocurrency payment completed successfully! Your invoice has been paid.');
        
        // Refresh invoice data to show updated status
        await fetchAllInvoiceData(false);
        
        console.log("CoinGate verification completed successfully - status should now be success");
        
        // CRITICAL: Return here to prevent further processing
        return;
        
      } else if (data.success && !data.is_paid && (data.payment_status === 'pending' || data.status === 'pending')) {
        // Payment is still processing - NO RETRY to prevent loops
        console.log("CoinGate payment is still pending");
        setPaymentStatus('verifying');
        setStatusMessage('Cryptocurrency payment is being processed. Please wait for blockchain confirmation...');
        
      } else if (!data.success) {
        console.error("CoinGate verification failed:", data.error || "Unknown error");
        setPaymentStatus('failed');
        
        // Provide more specific error messages
        let errorMessage = data.error || 'Payment verification failed.';
        if (errorMessage.includes('expired')) {
          errorMessage = 'Payment session has expired. Please try again.';
        } else if (errorMessage.includes('cancelled')) {
          errorMessage = 'Payment was cancelled.';
        } else if (errorMessage.includes('Order not found')) {
          // Check if invoice is already paid
          if (data.invoice_status === 'Paid') {
            setPaymentStatus('success');
            setStatusMessage('Payment completed successfully! Your invoice has been paid.');
            return;
          }
          errorMessage = 'Payment session not found. Please check your payment or try again.';
        }
        
        setStatusMessage(errorMessage);
        
      } else {
        console.error("CoinGate verification returned unexpected response:", data);
        setPaymentStatus('failed');
        setStatusMessage('Payment verification failed. Please contact support if your payment was processed.');
      }
    } catch (err) {
      console.error("CoinGate payment verification error:", err);
      setPaymentStatus('failed');
      setStatusMessage('Error verifying cryptocurrency payment. Please refresh the page or contact support.');
    }
  }, [getToken, fetchAllInvoiceData, paymentStatus]);


  // Add effect to check if invoice is paid and update payment status accordingly
  useEffect(() => {
    // If we're currently verifying and the invoice is now paid, update status to success
    if (paymentStatus === 'verifying' && data.invoice && data.invoice.status === 'Paid') {
      console.log('Invoice is now paid, updating payment status to success');
      setPaymentStatus('success');
      setStatusMessage('Payment completed successfully! Your invoice has been paid.');
      
      // Clear any stored payment info
      localStorage.removeItem('paypal_payment_info');
      localStorage.removeItem('stripe_payment_info');
    }
  }, [data.invoice?.status, paymentStatus]);

  // Initial data fetch
  useEffect(() => {
    fetchAllInvoiceData();
  }, [fetchAllInvoiceData]);

  // CRITICAL FIX: Aggressive URL parameter handling with immediate cleanup
  useEffect(() => {
    // Get URL search params immediately
    const searchParams = new URLSearchParams(location.search);
    
    // If no search params, nothing to process
    if (!location.search || location.search.length === 0) {
      return;
    }
    
    // CRITICAL: Clean URL immediately to prevent re-processing
    const newUrl = window.location.pathname;
    window.history.replaceState({}, document.title, newUrl);
    console.log('Cleaned URL immediately on detection');
    
    // Don't process if we're already in any active state
    if (paymentStatus !== 'idle') {
      console.log('Skipping URL processing - status is:', paymentStatus);
      return;
    }
    
    // Stripe parameters
    const paymentStatusParam = searchParams.get('payment_status');
    const sessionId = searchParams.get('session_id');
    
    // PayPal parameters
    const paypalToken = searchParams.get('token');
    const paypalPayerId = searchParams.get('PayerID') || searchParams.get('payer_id');
    
    // CoinGate parameters - UPDATED to use tracking token
    const cgToken = searchParams.get('cg_token');
    
    // Create unique identifier for this PayPal payment
    const paypalParamKey = `${paypalToken}-${paypalPayerId}`;
    
    console.log('URL parameters detected:', { 
      paymentStatusParam, 
      sessionId, 
      paypalToken,
      paypalPayerId,
      paypalParamKey,
      cgToken, // Updated parameter name
      invoiceId: id
    });
    
    // Check for Stripe return
    if (paymentStatusParam === 'success' && sessionId) {
      console.log('Detected successful Stripe payment, verifying...');
      setPaymentStatus('verifying');
      setStatusMessage('Verifying your payment...');
      verifyStripePayment(sessionId, id);
    }
    // Check for CoinGate return - UPDATED logic
    else if (paymentStatusParam === 'success' && cgToken) {
      console.log('Detected successful CoinGate payment, verifying with token...');
      setPaymentStatus('verifying');
      setStatusMessage('Verifying your cryptocurrency payment...');
      setTimeout(() => {
        verifyCoinGatePayment(cgToken, id);
      }, 100);
    }
    // Check for PayPal return
    else if (paypalToken && paypalPayerId) {
      // Check if we've already processed these exact parameters
      if (processedPayPalParams.current.has(paypalParamKey)) {
        console.log('PayPal parameters already processed, skipping');
        return;
      }
      
      // Mark these parameters as processed
      processedPayPalParams.current.add(paypalParamKey);
      
      console.log('Detected PayPal payment return, starting verification');
      
      setPaymentStatus('verifying');
      setStatusMessage('Verifying your PayPal payment...');
      
      // Start verification with the token as payment_id
      setTimeout(() => {
        verifyPayPalPayment(paypalToken, paypalPayerId, id);
      }, 100);
    }
    // Payment cancelled
    else if (paymentStatusParam === 'cancelled') {
      console.log('Payment was cancelled');
      setPaymentStatus('failed');
      setStatusMessage('Payment was cancelled.');
      
      // Clean up localStorage
      localStorage.removeItem('paypal_payment_info');
      localStorage.removeItem('stripe_payment_info');
      localStorage.removeItem('coingate_payment_info');
    }
  }, [location.search, id, paymentStatus, verifyStripePayment, verifyPayPalPayment, verifyCoinGatePayment]);

  // Memoized total due calculation
  const getTotalDue = useMemo(() => {
    if (!data.invoice) return 0;
    
    const subtotal = parseFloat(data.invoice.subtotal || 0);
    const tax = parseFloat(data.invoice.tax || 0);
    const credit = parseFloat(data.invoice.credit || 0);
    const voucher = parseFloat(data.invoice.voucher || 0);
    
    return Math.max(0, subtotal + tax - credit - voucher).toFixed(2);
  }, [data.invoice]);

  // Memoized total due calculation based on initial total minus payments from logs
  const calculateDisplayableBalance = useMemo(() => {
    if (!data.invoice || data.invoice.subtotal === undefined || data.invoice.tax === undefined) return '0.00';

    const initialTotal = parseFloat(data.invoice.subtotal || 0) + parseFloat(data.invoice.tax || 0);
    let deductionsSum = 0;

    if (data.transactionLogs && data.transactionLogs.length > 0) {
      data.transactionLogs.forEach(log => {
        const rawAmountStr = String(log.amount || '0');
        const cleanedAmountStr = rawAmountStr.replace(/[^0-9.]/g, ""); // Remove non-numeric chars except dot
        const amount = parseFloat(cleanedAmountStr);
        const type = String(log.type || '').toLowerCase();
        const status = String(log.status || '').toLowerCase();

        // Consider a log entry a "deduction" if it's a completed payment, credit, or voucher application.
        // These should have positive amounts.
        if ((status === 'completed' || status === 'paid' || status === 'success' || status === 'succeeded') && amount > 0) {
          // Check for known types that represent a payment or credit/voucher application
          if (
            type.includes('payment') || // Catches 'payment', 'stripe payment', 'card payment'
            type.includes('credit') || // Catches 'credit applied', 'account credit', 'direct credit application'
            type.includes('voucher')   // Catches 'voucher applied'
          ) {
            deductionsSum += amount;
          }
        }
      });
    }
    const balance = initialTotal - deductionsSum;
    return Math.max(0, balance).toFixed(2);
  }, [data.invoice, data.transactionLogs]);

  // Optimized modal toggle functions
  const toggleCreditModal = useCallback(() => {
    setModals(prev => ({
      ...prev,
      showCreditModal: !prev.showCreditModal,
      applyFullCredit: true,
      customCreditAmount: 0
    }));
  }, []);

  const toggleVoucherModal = useCallback(() => {
    // Don't open voucher modal for Credit Purchase invoices
    if (data.invoice?.type === 'Credit Purchase') {
      if (toast.current) {
        toast.current.show({
          severity: 'warning',
          summary: 'Not Allowed',
          detail: 'Vouchers cannot be applied to Credit Purchase invoices',
          life: 3000
        });
      }
      return;
    }
    
    setModals(prev => ({
      ...prev,
      showVoucherModal: !prev.showVoucherModal,
      voucherCode: ''
    }));
  }, [data.invoice]);

  // Form change handlers
  const handleApplyCreditChange = useCallback((e) => {
    setModals(prev => ({
      ...prev,
      applyFullCredit: e.target.value === 'full'
    }));
  }, []);

  const handleCustomCreditChange = useCallback((e) => {
    const inputValue = e.target.value;
    if (inputValue === '') {
      setModals(prev => ({
        ...prev,
        customCreditAmount: 0
      }));
      return;
    }
    setModals(prev => ({
      ...prev,
      customCreditAmount: parseFloat(inputValue) || 0
    }));
  }, []);

  const handleVoucherCodeChange = useCallback((e) => {
    setModals(prev => ({
      ...prev,
      voucherCode: e.target.value
    }));
  }, []);

  // Optimized API actions
  const handleApplyCredit = useCallback(async () => {
    const { invoice } = data;
    const { applyFullCredit, customCreditAmount } = modals;
    const amountToApply = applyFullCredit
      ? Math.min(data.accountCredit, parseFloat(invoice.total))
      : Math.min(customCreditAmount, data.accountCredit, parseFloat(invoice.total));

    setActionsState(prev => ({ ...prev, applyingCredit: true }));

    try {
      const token = getToken();
      const response = await fetch("/api.php?f=direct_credit_application", {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          invoice_id: id,
          amount: amountToApply
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const responseData = await response.json();
      if (!responseData.success) {
        throw new Error(responseData.error || "Credit application failed");
      }

      setModals(prev => ({ ...prev, showCreditModal: false }));

      // Update state efficiently
      if (responseData.invoice || responseData.transaction_logs || responseData.credit_balance) {
        setData(prev => ({
          ...prev,
          invoice: responseData.invoice || prev.invoice,
          transactionLogs: responseData.transaction_logs || prev.transactionLogs,
          accountCredit: responseData.credit_balance?.total_credit || prev.accountCredit
        }));
      }

      if (toast.current) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: `Applied €${amountToApply.toFixed(2)} account credit to invoice #${id}`,
          life: 3000
        });
      }

      if (responseData.fully_paid) {
        setTimeout(() => {
          if (toast.current) {
            toast.current.show({
              severity: 'info',
              summary: 'Invoice Paid',
              detail: 'The invoice has been fully paid with your account credit',
              life: 3000
            });
          }
        }, 500);
      }

      // Refresh data
      setTimeout(() => {
        fetchAllInvoiceData();
      }, 2000);
    } catch (err) {
      console.error("Error applying credit:", err);
      if (toast.current) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: `Failed to apply credit: ${err.message}`,
          life: 5000
        });
      }
    } finally {
      setActionsState(prev => ({ ...prev, applyingCredit: false }));
    }
  }, [data, modals, id, getToken, fetchAllInvoiceData]);

  const handleApplyVoucher = useCallback(async () => {
    const { voucherCode } = modals;
    const { invoice } = data;
    
    // Prevent applying vouchers to Credit Purchase invoices
    if (invoice.type === 'Credit Purchase') {
      if (toast.current) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Vouchers cannot be applied to Credit Purchase invoices',
          life: 3000
        });
      }
      return;
    }
    
    if (!voucherCode.trim()) {
      if (toast.current) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Please enter a valid voucher code',
          life: 3000
        });
      }
      return;
    }

    setActionsState(prev => ({ ...prev, applyingVoucher: true }));

    try {
      const token = getToken();
      const response = await fetch("/api.php?f=apply_voucher", {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          invoice_id: id,
          voucher_code: voucherCode.trim()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const responseData = await response.json();
      if (responseData.error) {
        throw new Error(responseData.error);
      }

      setModals(prev => ({ ...prev, showVoucherModal: false }));

      // Update state efficiently
      if (responseData.invoice || responseData.transaction_logs) {
        setData(prev => ({
          ...prev,
          invoice: responseData.invoice || prev.invoice,
          transactionLogs: responseData.transaction_logs || prev.transactionLogs
        }));
      }

      if (toast.current) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: `Voucher ${voucherCode} applied successfully! €${parseFloat(responseData.amount_applied).toFixed(2)} discount applied.`,
          life: 3000
        });
      }

      if (responseData.fully_paid) {
        setTimeout(() => {
          if (toast.current) {
            toast.current.show({
              severity: 'info',
              summary: 'Invoice Paid',
              detail: 'The invoice has been fully paid with your voucher',
              life: 3000
            });
          }
        }, 500);
      }

      // Refresh data
      setTimeout(() => {
        fetchAllInvoiceData();
      }, 2000);
    } catch (err) {
      console.error("Error applying voucher:", err);
      if (toast.current) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: `Failed to apply voucher: ${err.message}`,
          life: 5000
        });
      }
    } finally {
      setActionsState(prev => ({ ...prev, applyingVoucher: false }));
    }
  }, [modals, id, getToken, fetchAllInvoiceData, data]);

  const handlePay = useCallback(() => {
    // Show the payment modal
    setShowPaymentModal(true);
  }, []);

  // Lazy load PDF generation libraries only when needed
  const handlePrint = useCallback(() => {
    window.print();
  }, []);

  // Handle payment completion
  const handlePaymentComplete = useCallback(() => {
    setShowPaymentModal(false);
    // Refresh invoice data after payment
    fetchAllInvoiceData();
  }, [fetchAllInvoiceData]);
  
  const handleDownload = useCallback(() => {
    // Dynamically import libraries only when needed
    Promise.all([
      import('jspdf'),
      import('html2canvas')
    ]).then(([jsPDFModule, html2canvasModule]) => {
      const jsPDF = jsPDFModule.default;
      const html2canvas = html2canvasModule.default;
      
      const invoiceContent = document.getElementById('invoice-content');
      if (!invoiceContent) return;
      
      html2canvas(invoiceContent, {
        scale: 2,
        useCORS: true,
        logging: false
      }).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4',
          compress: true
        });
        
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();
        const imgWidth = canvas.width;
        const imgHeight = canvas.height;
        const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
        const imgX = (pdfWidth - imgWidth * ratio) / 2;
        const imgY = 10;
        
        pdf.addImage(imgData, 'PNG', imgX, imgY, imgWidth * ratio, imgHeight * ratio);
        pdf.save(`Invoice-${data.invoice?.id || id}.pdf`);
      });
    }).catch(err => {
      console.error("Error loading PDF libraries:", err);
      if (toast.current) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to generate PDF. Please try again.',
          life: 3000
        });
      }
    });
  }, [data.invoice, id]);

  // Memoized utility functions
  const getStatusBadge = useCallback((status) => {
    if (!status) return 'bg-secondary';

    switch(status.toLowerCase()) {
      case 'completed': return 'bg-success';
      case 'pending': return 'bg-warning';
      case 'failed': return 'bg-danger';
      case 'refunded': return 'bg-info';
      default: return 'bg-secondary';
    }
  }, []);

  const getInvoiceStatus = useCallback(() => {
    const { invoice } = data;
    if (!invoice) return '';

    if (invoice.status === 'Paid') {
      return invoice.type === 'Credit Purchase' && invoice.credit_applied === 1 
        ? 'Credit Added' 
        : 'Paid';
    } 
    return invoice.status === 'Partially Paid' ? 'Partially Paid' : 'Unpaid';
  }, [data.invoice]);

  const getInvoiceStatusBadgeClass = useCallback(() => {
    const { invoice } = data;
    if (!invoice) return '';

    if (invoice.status === 'Paid') {
      return invoice.type === 'Credit Purchase' && invoice.credit_applied === 1
        ? 'bg-info'
        : 'bg-success';
    }
    return invoice.status === 'Partially Paid' ? 'bg-warning' : 'bg-danger';
  }, [data.invoice]);



  // Payment Status Alert component - FIXED: No infinite loops
  const PaymentStatusAlert = useMemo(() => {
    if (paymentStatus === 'idle') return null;

    if (paymentStatus === 'verifying') {
      return (
        <div className="alert alert-info mb-4" role="alert">
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status" style={{paddingRight: '0.5rem'}}></div>
              <div>{statusMessage || 'Verifying your payment... Please wait.'}</div>
            </div>
            <div className="d-flex gap-2">
              {/* Force success button if invoice is already paid */}
              {data.invoice?.status === 'Paid' && (
                <button 
                  className="btn btn-sm btn-success"
                  onClick={() => {
                    console.log('Force setting payment status to success - invoice is already paid');
                    setPaymentStatus('success');
                    setStatusMessage('Payment completed successfully! Your invoice has been paid.');
                    localStorage.removeItem('paypal_payment_info');
                    localStorage.removeItem('stripe_payment_info');
                  }}
                >
                  Complete Verification
                </button>
              )}
            </div>
          </div>
        </div>
      );
    }

    if (paymentStatus === 'success') {
      return (
        <div className="alert alert-success mb-4" role="alert">
          <div className="d-flex align-items-center">
            <i className="fa fa-check-circle me-2" style={{paddingRight: '0.5rem'}}></i>
            <div>{statusMessage || 'Payment successful! Your invoice has been paid.'}</div>
          </div>
        </div>
      );
    }

    if (paymentStatus === 'failed') {
      return (
        <div className="alert alert-danger mb-4" role="alert">
          <div className="d-flex align-items-center">
            <i className="fa fa-exclamation-circle me-2" style={{paddingRight: '0.5rem'}}></i>
            <div>{statusMessage || 'Payment verification failed.'}</div>
          </div>
        </div>
      );
    }

    return null;
  }, [paymentStatus, statusMessage, data.invoice?.status]);

  // Memoized table component
  const InvoiceItemsTable = useMemo(() => {
    const { invoice } = data;
    if (!invoice) return null;

    return (
      <div className="table-responsive mb-4">
        <table className="table table-striped invoice-items-table">
          <thead className="bg-light">
            <tr>
              <th style={{width: "5%", textAlign: "center"}}>#</th>
              <th style={{width: "40%"}}>Description</th>
              <th style={{width: "15%"}}>Period</th>
              <th style={{width: "12%", textAlign: "right"}}>Price</th>
              <th style={{width: "15%", textAlign: "center"}}>Qty</th>
              <th style={{width: "15%", textAlign: "right"}}>Total</th>
            </tr>
          </thead>
          <tbody>
            {invoice.items && invoice.items
              .filter(item => !(
                 item.description && (
                   item.description.includes("Account Credit") ||
                   item.description.includes("Credit Applied") ||
                   item.description.toLowerCase().includes("vat") ||
                   item.description.toLowerCase().includes("tax")
                 )
              ))
              .map((item, index) => (
                <tr key={item.id || index}>
                  <td style={{textAlign: "center"}}>{index + 1}</td>
                  <td>
                    <div className="font-weight-medium">{item.description}</div>
                    {item.details && <div className="text-muted small">{item.details}</div>}
                  </td>
                  <td>{item.period || '-'}</td>
                  <td style={{textAlign: "right"}}>€{parseFloat(item.price).toFixed(2)}</td>
                  <td style={{textAlign: "center"}}>{item.quantity}</td>
                  <td style={{textAlign: "right", fontWeight: "500"}}>€{parseFloat(item.total).toFixed(2)}</td>
                </tr>
              ))
            }
          </tbody>
          <tfoot>
            <tr className="border-top">
              <td colSpan="4" className="border-0"></td>
              <th className="text-right">Subtotal</th>
              <td className="text-right">€{parseFloat(invoice.subtotal || 0).toFixed(2)}</td>
            </tr>
            {invoice.tax > 0 && (
              <tr>
                <td colSpan="4" className="border-0"></td>
                <th className="text-right">VAT {invoice.taxRate ? `(${invoice.taxRate}%)` : ''}</th>
                <td className="text-right">€{parseFloat(invoice.tax || 0).toFixed(2)}</td>
              </tr>
            )}
            {parseFloat(invoice.voucher || 0) > 0 && (
              <tr>
                <td colSpan="4" className="border-0"></td>
                <th className="text-right">Voucher Discount</th>
                <td className="text-right text-success">-€{parseFloat(invoice.voucher || 0).toFixed(2)}</td>
              </tr>
            )}
            {parseFloat(invoice.credit) !== 0 && (
              <tr>
                <td colSpan="4" className="border-0"></td>
                <th className="text-right">Credit Applied</th>
                <td className="text-right">-€{Math.abs(parseFloat(invoice.credit || 0)).toFixed(2)}</td>
              </tr>
            )}
            <tr>
              <td colSpan="4" className="border-0"></td>
              <th className="text-right">TOTAL</th>
              <td className="text-right">€{getTotalDue}</td>
            </tr>
          </tfoot>
        </table>
      </div>
    );
  }, [data.invoice, getTotalDue]);

  // Calculate amounts for PaymentModal
  const finalPayableNum = parseFloat(calculateDisplayableBalance);
  const isCreditPurchaseInvoice = data.invoice?.type === 'Credit Purchase';

  let paymentModalProps;

  if (isCreditPurchaseInvoice) {
    // For credit purchases, VAT is not applied.
    // finalPayableNum is assumed to be the VAT-exclusive amount from calculateDisplayableBalance.
    paymentModalProps = {
      totalAmount: finalPayableNum, // This will be subTotal for PaymentModal
      vatAmount: 0.00,
      totalWithVat: finalPayableNum, // Grand total for PaymentModal
      vatRate: "0", // VAT rate as a string for PaymentModal
    };
  } else {
    // For regular invoices, calculate VAT based on finalPayableNum.
    // finalPayableNum is assumed to be the grand total (VAT-inclusive if applicable).
    const effectiveVatRateStr = data.invoice?.taxRate || data.vatInfo?.vat_rate;
    let calculatedVatAmount = 0.00;
    let calculatedSubtotalAmount = finalPayableNum;

    if (finalPayableNum > 0 && effectiveVatRateStr && parseFloat(effectiveVatRateStr) > 0) {
      const vatRateDecimal = parseFloat(effectiveVatRateStr) / 100;
      // Calculate VAT component from a VAT-inclusive total
      const rawVat = finalPayableNum * (vatRateDecimal / (1 + vatRateDecimal));
      calculatedVatAmount = parseFloat(rawVat.toFixed(2));
      calculatedSubtotalAmount = parseFloat((finalPayableNum - calculatedVatAmount).toFixed(2));
    }

    paymentModalProps = {
      totalAmount: calculatedSubtotalAmount,
      vatAmount: calculatedVatAmount,
      totalWithVat: finalPayableNum,
      vatRate: effectiveVatRateStr,
    };
  }

  // Render loading state
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "200px" }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden"></span>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h4 className="alert-heading">Error loading invoice!</h4>
        <p>{error}</p>
        <hr />
        <p className="mb-0">
          Please try again or <Link to="/billing/invoices">return to invoices</Link>
        </p>
      </div>
    );
  }

  // If no invoice data
  if (!data.invoice) {
    return (
      <div className="alert alert-danger" role="alert">
        Invoice not found. <Link to="/billing/invoices">Return to invoices</Link>
      </div>
    );
  }

  // Destructure for cleaner template
  const { invoice, transactionLogs, accountCredit } = data;
  const { applyingCredit, applyingVoucher } = actionsState;
  const { 
    showCreditModal, 
    showVoucherModal, 
    applyFullCredit, 
    customCreditAmount, 
    voucherCode 
  } = modals;

  return (
    <>
      <Toast ref={toast} />
      
      {/* Payment Modal */}
      <PaymentModal 
        isOpen={showPaymentModal}
        toggle={() => setShowPaymentModal(false)}
        totalAmount={paymentModalProps.totalAmount}
        onComplete={handlePaymentComplete}
        invoiceId={id}
        isCreditsInvoice={isCreditPurchaseInvoice}
        vatRate={paymentModalProps.vatRate}
        vatAmount={paymentModalProps.vatAmount}
        totalWithVat={paymentModalProps.totalWithVat}
      />
      
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1"><Link to="/billing/invoices">Invoices</Link></li>
            <li className="breadcrumb-item1">Invoice #{invoice.id}</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="btn-group">
            <button className="btn btn-sm btn-outline-primary me-2" onClick={handleDownload}>
              <i className="fa fa-download me-1"></i> Download
            </button>
            <button className="btn btn-sm btn-outline-secondary" onClick={handlePrint}>
              <i className="fa fa-print me-1"></i> Print
            </button>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">


          {/* Payment Status Alert - Display payment verification status */}
          {PaymentStatusAlert}

          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center py-2">
              <div className="d-flex align-items-center">
                <h4 className="card-title mb-0">Invoice #{invoice.id}</h4>
                <span className={`ms-2 badge ${getInvoiceStatusBadgeClass()}`}>
                  {getInvoiceStatus()}
                </span>
              </div>

              {/* Payment options - only show if invoice is not paid */}
              {invoice.status !== 'Paid' && (
                <div className="d-flex">
                  {/* Don't show voucher button for Credit Purchase invoices */}
                  {invoice.type !== 'Credit Purchase' && (
                    <button className="btn btn-primary btn-sm me-2" onClick={toggleVoucherModal}>
                      <i className="fa fa-ticket-alt me-1"></i> Apply Voucher
                    </button>
                  )}
                  {accountCredit > 0 && invoice.type !== 'Credit Purchase' && (
                    <button className="btn btn-info btn-sm me-2" onClick={toggleCreditModal}>
                      <i className="fa fa-wallet me-1"></i> Apply Credit
                    </button>
                  )}
                  <button
                    className="btn btn-success btn-sm"
                    onClick={handlePay}
                  >
                    <i className="fa fa-credit-card" style={{ paddingRight: "0.5rem" }}></i>
                    Pay Now (€{calculateDisplayableBalance})
                  </button>
                </div>
              )}
            </div>

            <div className="card-body p-3" id="invoice-content">
              <div className="row mb-3">
                {/* Invoice details section */}
                <div className="col-12 d-flex justify-content-between align-items-start mb-4">
                  <div>
                    <small className="text-muted d-block">From:</small>
                    <strong>{invoice.companyInfo?.name || ""}</strong>
                    <address className="mb-0" style={{fontSize: "0.85rem", lineHeight: "1.3"}}>
                      {invoice.companyInfo?.address || ""}<br />
                      {invoice.companyInfo?.city || ""}{invoice.companyInfo?.zip ? `, ${invoice.companyInfo.zip}` : ""}<br />
                      {invoice.companyInfo?.country || ""}<br />
                      {invoice.companyInfo?.vatId ? `VAT ID: ${invoice.companyInfo.vatId}` : ""}
                    </address>
                  </div>
                  <div>
                    <small className="text-muted d-block">To:</small>
                    <strong>{invoice.billingInfo?.name || ""}</strong>
                    <address className="mb-0" style={{fontSize: "0.85rem", lineHeight: "1.3"}}>
                      {invoice.billingInfo?.company && <>{invoice.billingInfo.company}<br /></>}
                      {invoice.billingInfo?.address || ""}<br />
                      {invoice.billingInfo?.city || ""}{invoice.billingInfo?.country ? `, ${invoice.billingInfo.country}` : ""}<br />
                      {invoice.billingInfo?.vatId && <>VAT ID: {invoice.billingInfo.vatId}</>}
                    </address>
                  </div>
                </div>

                <div className="col-12 d-flex justify-content-between align-items-start mb-4">
                  <div>
                    <div style={{fontSize: "0.85rem", lineHeight: "1.5"}}>
                      <div><strong>Number:</strong> {invoice.number}</div>
                      <div><strong>Type:</strong> {invoice.type}</div>
                      <div><strong>Issue Date:</strong> {invoice.issueDate}</div>
                      <div><strong>Due Date:</strong> {invoice.dueDate}</div>
                    </div>
                  </div>
                  <div className="text-end">
                    <div className="mb-1"><strong style={{fontSize: "1.1rem"}}>Total Due</strong></div>
                    <h2 className="text-success mb-0">€{calculateDisplayableBalance}</h2>
                  </div>
                </div>
              </div>

              {/* Invoice items table */}
              {InvoiceItemsTable}
            </div>

            {/* Transaction Logs Section */}
            <div className="card-body p-3 border-top">
              <h5 className="mb-3">
                <i className="fa fa-history me-2"></i>
                Transaction Logs
              </h5>

              <div className="table-responsive">
                <table id="transaction-logs-table" className="table table-sm table-striped table-bordered">
                  <thead className="bg-light">
                    <tr>
                      <th width="15%">Transaction ID</th>
                      <th width="15%">Date</th>
                      <th width="15%">Type</th>
                      <th width="10%">Amount</th>
                      <th width="25%">Description</th>
                      <th width="10%">Status</th>
                      <th width="10%">Processor</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactionLogs.length > 0 ? (
                      transactionLogs.map((transaction, index) => (
                        <tr key={transaction.id || index}>
                          <td>{transaction.id || '-'}</td>
                          <td>{transaction.date || '-'}</td>
                          <td>{transaction.type || '-'}</td>
                          <td>{transaction.amount || '-'}</td>
                          <td>{transaction.description || '-'}</td>
                          <td>
                            <span className={`badge ${getStatusBadge(transaction.status)}`}>
                              {transaction.status || 'Unknown'}
                            </span>
                          </td>
                          <td>{transaction.processor || '-'}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="7" className="text-center py-3">No transaction records found</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="card-footer d-flex justify-content-between py-2">
              <Link to="/billing/invoices" className="btn btn-sm btn-outline-danger">
                <i className="fa fa-arrow-left me-1"></i> Back to Invoices
              </Link>
              <div>
                <small className="text-muted">If you have any questions, please contact <a href="mailto:<EMAIL>"><EMAIL></a></small>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Credit Application Modal */}
      {showCreditModal && (
        <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Apply Account Credit</h5>
                <button type="button" className="close" onClick={toggleCreditModal}>
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div className="modal-body">
                <div className="alert alert-info">
                  <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                  You have <strong>€{accountCredit.toFixed(2)}</strong> available credit in your account.
                </div>

                <div className="form-group mb-3">
                  <label className="form-label">How much credit would you like to apply?</label>
                  <div className="mt-3">
                    <div className="form-check mb-3" style={{display: 'flex', alignItems: 'flex-start'}}>
                      <input
                        type="radio"
                        className="form-check-input"
                        id="apply-full"
                        name="apply-amount"
                        value="full"
                        checked={applyFullCredit}
                        onChange={handleApplyCreditChange}
                        style={{marginTop: '15px'}}
                      />
                      <label className="form-check-label" htmlFor="apply-full" style={{marginLeft: '8px'}}>
                        <strong>Apply maximum credit</strong>
                        <br />
                        <small className="text-muted">
                          €{Math.min(accountCredit, parseFloat(invoice.total)).toFixed(2)} will be applied to this invoice
                        </small>
                      </label>
                    </div>

                    <div className="form-check" style={{display: 'flex', alignItems: 'flex-start'}}>
                      <input
                        type="radio"
                        className="form-check-input"
                        id="apply-custom"
                        name="apply-amount"
                        value="custom"
                        checked={!applyFullCredit}
                        onChange={handleApplyCreditChange}
                        style={{marginTop: '15px'}}
                      />
                      <label className="form-check-label" htmlFor="apply-custom" style={{marginLeft: '8px'}}>
                        <strong>Apply custom amount</strong>
                        <br />
                        <small className="text-muted">
                          Choose a specific amount to apply
                        </small>
                      </label>
                    </div>
                  </div>
                </div>

                {!applyFullCredit && (
                  <div className="form-group">
                    <div className="input-group">
                      <div className="input-group-prepend">
                        <span className="input-group-text">€</span>
                      </div>
                      <input
                        type="number"
                        className="form-control"
                        placeholder="Enter amount"
                        min="0.01"
                        max={Math.min(accountCredit, parseFloat(invoice.total))}
                        step="0.01"
                        value={customCreditAmount === 0 ? '' : customCreditAmount}
                        onChange={handleCustomCreditChange}
                      />
                    </div>
                    <small className="form-text text-muted">
                      Enter an amount up to €{Math.min(accountCredit, parseFloat(invoice.total)).toFixed(2)}
                    </small>
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={toggleCreditModal}>Cancel</button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleApplyCredit}
                  disabled={applyingCredit || (!applyFullCredit && (!customCreditAmount || customCreditAmount <= 0))}
                >
                  {applyingCredit ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Processing
                    </>
                  ) : (
                    'Apply Credit'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Voucher Application Modal */}
      {showVoucherModal && (
        <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Apply Voucher</h5>
                <button type="button" className="close" onClick={toggleVoucherModal}>
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div className="modal-body">
                <div className="alert alert-info">
                  <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                  Enter a valid voucher code to apply a discount to this invoice.
                </div>

                <div className="form-group">
                  <label className="form-label">Voucher Code</label>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Enter voucher code"
                    value={voucherCode}
                    onChange={handleVoucherCodeChange}
                  />
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={toggleVoucherModal}>Cancel</button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleApplyVoucher}
                  disabled={applyingVoucher || !voucherCode.trim()}
                >
                  {applyingVoucher ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Processing
                    </>
                  ) : (
                    'Apply Voucher'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default InvoiceDetails;