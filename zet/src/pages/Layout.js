import { Outlet, Link, useParams, useLocation, useNavigate } from "react-router-dom";
const Layout = () => {
const path = useLocation();
function splitLastOccurrence(str, substring) {
  const lastIndex = str.lastIndexOf(substring);
  const before = str.slice(0, lastIndex);
  const after = str.slice(lastIndex + 1);
  return after;
}

  return (
    <>
		<div className="page">
			<div className="page-main">
				<div className="app-sidebar2">
					<div className="app-sidebar__logo">
						<Link className="header-brand" to="/">
							<img src="/assets/images/brand/logo.png" className="header-brand-img desktop-lgo" alt=""/>
							<img src="/assets/images/brand/logo1.png" className="header-brand-img dark-logo" alt=""/>
							<img src="/assets/images/brand/favicon.png" className="header-brand-img mobile-logo" alt=""/>
							<img src="/assets/images/brand/favicon1.png" className="header-brand-img darkmobile-logo" alt=""/>
						</Link>
					</div>
				</div>
				<aside className="app-sidebar app-sidebar3">
					<ul className="side-menu">
						<li className="slide">
							<Link data-toggle="sidebar1" className={(splitLastOccurrence(path.pathname, '/') == '') ? "side-menu__item active" : "side-menu__item"} to="/">
							<i className="side-menu__icon si si-layers"></i>
							<span className="side-menu__label">Services</span><i className="angle fa fa-angle-right"></i></Link>
						</li>
						<li className="slide">
							<Link data-toggle="sidebar1" className={(splitLastOccurrence(path.pathname, '/') == 'credit' || splitLastOccurrence(path.pathname, '/') == 'invoices' || splitLastOccurrence(path.pathname, '/') == 'history' || splitLastOccurrence(path.pathname, '/') == 'methods') ? "side-menu__item active" : "side-menu__item"} to="/billing/credit">
							<i className="side-menu__icon si si-wallet"></i>
							<span className="side-menu__label">Billing</span><i className="angle fa fa-angle-right"></i></Link>
						</li>

						<li className="slide">
							<Link data-toggle="sidebar1" className={(splitLastOccurrence(path.pathname, '/') == 'support' || splitLastOccurrence(path.pathname, '/') == 'newcase') ? "side-menu__item active" : "side-menu__item"} to="/support">

							<i className="side-menu__icon si si-support"></i>
							<span className="side-menu__label">Support</span><i className="angle fa fa-angle-right"></i></Link>
						</li>


						<li className="slide">
							<Link data-toggle="sidebar1" className={(path.pathname.startsWith('/reseller')) ? "side-menu__item active" : "side-menu__item"} to="/reseller">

							<i className="side-menu__icon si si-people"></i>
							<span className="side-menu__label">Affiliate</span><i className="angle fa fa-angle-right"></i></Link>
						</li>

						<li className="slide">
							<Link data-toggle="sidebar1" className={(splitLastOccurrence(path.pathname, '/') == 'faq') ? "side-menu__item active" : "side-menu__item"} to="/faq">
							<i className="side-menu__icon si si-briefcase"></i>
							<span className="side-menu__label">FAQ</span><i className="angle fa fa-angle-right"></i></Link>
						</li>

					</ul>
				</aside>

				<div className="app-content main-content">
					<div className="side-app">

						<div className="app-header header top-header">
							<div className="container-fluid">
								<div className="d-flex">
									<a className="header-brand" href="index.html">
										<img src="/assets/images/brand/logo.png" className="header-brand-img desktop-lgo" alt="Dashtic logo"/>
										<img src="/assets/images/brand/logo1.png" className="header-brand-img dark-logo" alt="Dashtic logo"/>
										<img src="/assets/images/brand/favicon.png" className="header-brand-img mobile-logo" alt="Dashtic logo"/>
										<img src="/assets/images/brand/favicon1.png" className="header-brand-img darkmobile-logo" alt="Dashtic logo"/>
									</a>
									<div className="dropdown side-nav">
										<div className="app-sidebar__toggle" data-toggle="sidebar">
											<a className="open-toggle" href="#">
												<svg className="header-icon mt-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg>
											</a>
											<a className="close-toggle" href="#">
												<svg className="header-icon mt-1" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg>
											</a>
										</div>
									</div>

									<div className="d-flex order-lg-2 ml-auto">

											<Link to="/account/details" className="nav-link pr-0">
												<button className="btn btn-default">Account</button>
											</Link>
											<Link to="/logout" className="nav-link pr-0">
												<button className="btn btn-default">Logout</button>
											</Link>
									</div>
								</div>
							</div>

						</div>

      				<Outlet />
		</div>
	</div>
    </div>

			<footer className="footer">
				<div className="container">
					<div className="row align-items-center flex-row-reverse">
						<div className="col-md-12 col-sm-12 mt-3 mt-lg-0 text-center">
							Copyright © 2024 <a href="#">ZetServers</a> All rights reserved.
						</div>
					</div>
				</div>
			</footer>

      </div>




   </>
  )
};

export default Layout;