import React, { useState } from "react"
import { Outlet, Link } from "react-router-dom";
import DiscountAlert from "../components/DiscountAlert";


const FAQ = () => {
  // State for handling accordion functionality for categories
  const [expandedCategories, setExpandedCategories] = useState({});

  const toggleCategory = (categoryIndex) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryIndex]: !prev[categoryIndex]
    }));
  };

  // FAQ data organized by categories
  const faqData = [
    {
      category: "General",
      questions: [
        {
          question: "What is ZetServers?",
          answer: "ZetServers is a provider of dedicated servers, cloud services, colocation, and IP transit solutions designed for businesses and individuals who need reliable and high-performance hosting infrastructure."
        },
        {
          question: "How do I get started with ZetServers?",
          answer: "To get started, create an account and select a service that fits your needs. Our platform offers various services including dedicated servers, cloud servers, colocation, and IP transit. Once you've selected a service, follow the checkout process, and your service will be provisioned according to the estimated delivery time."
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept multiple payment methods including credit cards, cryptocurrency, and bank transfers. You can manage your payment preferences in your account settings."
        }
      ]
    },
    {
      category: "Dedicated Servers",
      questions: [
        {
          question: "What is included with dedicated server hosting?",
          answer: "Our dedicated server hosting includes the physical server hardware with your chosen specifications, network connectivity with the selected bandwidth plan, IP addresses, a control panel for server management, and 24/7 technical support for infrastructure-related issues."
        },
        {
          question: "How long does it take to set up a dedicated server?",
          answer: "Setup time varies depending on the server configuration and datacenter location. Most standard configurations are available instantly or within 24 hours. Custom configurations may take 3-7 business days. The estimated delivery time is shown during the ordering process."
        },
        {
          question: "Can I upgrade my server after ordering?",
          answer: "Yes, you can upgrade various components of your server including storage, bandwidth, and IP subnets. Some upgrades can be done instantly through your control panel, while hardware upgrades may require scheduled maintenance."
        }
      ]
    },
    {
      category: "Cloud Services",
      questions: [
        {
          question: "What is the difference between cloud servers and dedicated servers?",
          answer: "Cloud servers are virtualized environments running on our cloud infrastructure, offering flexibility, rapid deployment, and easy scalability. Dedicated servers provide exclusive access to physical hardware for maximum performance and control. Cloud servers are ideal for variable workloads, while dedicated servers are better for consistent, high-performance applications."
        },
        {
          question: "How quickly can I scale my cloud server resources?",
          answer: "Cloud server resources can typically be scaled instantly for components like CPU and RAM. Storage expansions are also quick but may take a few minutes to complete. All scaling operations can be performed through your control panel without service interruption."
        },
        {
          question: "Are cloud servers backed up automatically?",
          answer: "Basic system snapshots are included with our cloud servers, but we recommend implementing a comprehensive backup strategy for your data. Additional backup options are available as add-on services in your control panel."
        }
      ]
    },
    {
      category: "Colocation",
      questions: [
        {
          question: "What is colocation?",
          answer: "Colocation involves housing your own server hardware in our secure data centers. We provide the rack space, power, cooling, and network connectivity, while you maintain ownership and control of your hardware."
        },
        {
          question: "Can you help with hardware installation for colocation?",
          answer: "Yes, we offer remote hands services for hardware installation, maintenance, and troubleshooting. Our technicians can handle equipment installation, replacement, and basic configuration tasks upon request."
        },
        {
          question: "How secure are your colocation facilities?",
          answer: "Our colocation facilities feature multiple layers of physical security including biometric access controls, 24/7 surveillance, motion detection, and on-site security personnel. All facilities are also equipped with advanced fire suppression and climate control systems."
        }
      ]
    },
    {
      category: "Billing & Account",
      questions: [
        {
          question: "How does your billing cycle work?",
          answer: "Our services are billed on a recurring basis, which can be monthly, quarterly, semi-annually, or annually depending on your preference. You can adjust your billing cycle in your account settings. Longer billing cycles typically come with discounts."
        },
        {
          question: "What happens if I miss a payment?",
          answer: "If a payment is missed, we'll send multiple notifications to your registered email address. Services remain active for a grace period, after which they may be suspended. To prevent service interruption, we recommend enabling auto-payments or setting up account credit."
        },
        {
          question: "How do I cancel my service?",
          answer: "You can cancel your service through your control panel. Navigate to the specific service you want to cancel and select the cancellation option. You can choose to cancel immediately or at the end of your current billing cycle. Please note that immediate cancellations do not provide refunds for the remaining prepaid period."
        }
      ]
    },
    {
      category: "Technical Support",
      questions: [
        {
          question: "What kind of technical support do you offer?",
          answer: "We provide 24/7 technical support for infrastructure-related issues. Our services are generally unmanaged, which means you're responsible for your own operating system and application management. For matters beyond your control (network issues, hardware failures, etc.), our Network Operations Center (NOC) is available around the clock."
        },
        {
          question: "How do I contact technical support?",
          answer: "Technical support is available through our ticket system in your client area. For urgent matters, we recommend opening a ticket with 'High' or 'Urgent' priority. Keep in mind that our Sales and Billing departments operate Monday through Friday, 9:00 AM to 6:00 PM GMT+3."
        },
        {
          question: "Do you offer managed services?",
          answer: "While our standard offerings are unmanaged, we do provide optional managed service add-ons for customers who need assistance with server administration, security hardening, monitoring, or application management. These services can be added to your account through your control panel."
        }
      ]
    }
  ];

  return (
    <>
      <div className="page-header">
      <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">FAQ</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card">
            <div className="card-header">
              <div className="card-title">Frequently Asked Questions</div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                {faqData.map((category, categoryIndex) => (
                  <div className="accordion-item" key={categoryIndex}>
                    <div 
                      className="accordion-button collapsed" 
                      onClick={() => toggleCategory(categoryIndex)}
                      style={{ 
                        cursor: 'pointer', 
                        padding: '15px',
                        background: '#f8f9fa',
                        borderBottom: '1px solid #e9ecef',
                        color: '#495057',
                        fontWeight: 'bold',
                        fontSize: '18px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        width: '100%'
                      }}
                    >
                      <div>{category.category}</div>
                      <i className={`fa fa-chevron-${expandedCategories[categoryIndex] ? 'up' : 'down'}`}></i>
                    </div>
                    
                    <div 
                      style={{ 
                        display: expandedCategories[categoryIndex] ? 'block' : 'none',
                        padding: '20px',
                        borderBottom: '1px solid #e9ecef'
                      }}
                    >
                      {category.questions.map((faq, faqIndex) => (
                        <div className="mb-4" key={`${categoryIndex}-${faqIndex}`}>
                          <h5 style={{ fontWeight: 'bold', color: '#212529' }}>{faq.question}</h5>
                          <p style={{ color: '#6c757d', marginTop: '10px' }}>{faq.answer}</p>
                          {faqIndex < category.questions.length - 1 && <hr style={{ margin: '20px 0' }} />}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="row mt-5">
                <div className="col-md-12">
                  <div className="card">
                    <div className="card-header">
                      <h5 className="card-title">Still have questions?</h5>
                    </div>
                    <div className="card-body">
                      <div className="row align-items-center">
                        <div className="col-md-9">
                          <p className="mb-0">If you couldn't find answers to your questions in our FAQ, our support team is ready to help you.</p>
                        </div>
                        <div className="col-md-3 text-right">
                          <Link to="/support/newcase" className="btn btn-primary">Contact Support</Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FAQ;