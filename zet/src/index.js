import ReactDOM from "react-dom/client";
import React, { useEffect, useState } from "react";
import { BrowserRouter as Router, Routes, Link, Route } from "react-router-dom";
import Login from "./pages/Login";
import Logout from "./pages/Logout";
import Register from "./pages/Register";
import LoginRedirect from "./pages/LoginRedirect";
import Layout from "./pages/Layout";
import Home from "./pages/Home";
import DedicatedOrder from "./pages/DedicatedOrder";
import CloudOrder from "./pages/CloudOrder";
import ColocationOrder from "./pages/ColocationOrder";
import ColocationAssetsOrder from "./pages/ColocationAssetsOrder";
import IPTransitOrder from "./pages/IPTransitOrder";
import Billing from "./pages/Billing";
import InvoiceDetails from "./pages/InvoiceDetails";  // Import the new component
import TransactionDetails from "./pages/TransactionDetails";  // Import the new component
import Support from "./pages/Support";
import Ticket from "./pages/Ticket";
import NewTicket from "./pages/NewTicket";
import Account from "./pages/Account";
import NoPage from "./pages/NoPage";
import AcceptInvitation from './pages/AcceptInvitation';
import Reseller from "./pages/Reseller.js";
import Faq from "./pages/Faq.js";
import ServerDetailsView from "./pages/ServerDetailsView.js"
import VpsServerDetailsView from "./pages/VpsServerDetailsView.js"
import CloudServerDetailsView from './pages/CloudServerDetailsView';
import IPTransitDetailsView from './pages/IPTransitDetailsView.js';
import ColocationDetailsView from './pages/ColocationDetailsView.js';
import ColocationAssetsDetailsView from './pages/ColocationAssetsDetailsView.js';
import './index.css';

import LoginChecker from "./pages/LoginChecker.js";
import ScrollToTop from "./components/ScrollToTop.js";

// Create a new file called DismissibleAlert.js and import it here
// import DismissibleAlert from './DismissibleAlert';

function setToken(userToken) {
  // Store token in both localStorage (for persistence across tabs) and sessionStorage (for backward compatibility)
  localStorage.setItem('token', userToken);
  sessionStorage.setItem('token', userToken);
  window.location.reload(false);
}

function getToken() {
  // Try to get token from localStorage first, then fall back to sessionStorage
  const localToken = localStorage.getItem('token');
  const sessionToken = sessionStorage.getItem('token');
  return localToken || sessionToken;
}

const delay = ms => new Promise(
  resolve => setTimeout(resolve, ms)
);

async function parseLoginCheck() {
    // Get token from our getToken function to ensure we check the right token
    const token = getToken();
    if (!token) {
      return { error: 1, message: 'No token available' };
    }

    const prepared_data = {'token' : token}
    try {
      const response = await fetch('/api.php?f=login_check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(prepared_data)
      });
      
      const text = await response.text();
      
      try {
        // Try to parse the response as JSON
        return JSON.parse(text);
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        console.error('Raw response:', text);
        // Return a default response to prevent crashes
        return { error: 1, message: 'Invalid JSON response from server' };
      }
    } catch (error) {
      console.error('Error checking login:', error);
      // Return a default response to prevent crashes
      return { error: 1, message: 'Network error during login check' };
    }
}


async function loginCheck() {
    while(true){
    await delay(60000);
    const response = await parseLoginCheck()
    	if(response['error'] == 1 || response['error'] == 5){
        	// Clear both storage locations
        	console.log("Session expired during periodic check, clearing storage");
        	localStorage.removeItem('token');
        	sessionStorage.removeItem('token');
        	sessionStorage.clear();
  		window.location.reload(false);
    	}
   }
}

function MainApp() {
  const [isValidSession, setIsValidSession] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const token = getToken();

  // Immediately check token validity when component mounts or a new tab is opened
  useEffect(() => {
    async function validateToken() {
      if (!token) {
        setIsValidSession(false);
        setIsLoading(false);
        return;
      }

      try {
        console.log("Validating token on page load/new tab...");
        const response = await parseLoginCheck();
        console.log("Token validation response:", response);

        if (response && (response.error == 1 || response.error == 5)) {
          console.log("Token is invalid, clearing storage");
          // Token is invalid, clear storage
          localStorage.removeItem('token');
          sessionStorage.removeItem('token');
          sessionStorage.clear();
          setIsValidSession(false);
        } else {
          console.log("Token is valid");
          setIsValidSession(true);

          // Ensure token is in both storage locations
          if (localStorage.getItem('token') !== sessionStorage.getItem('token')) {
            console.log("Syncing token between storage locations");
            sessionStorage.setItem('token', token);
          }
        }
      } catch (error) {
        console.error("Error validating token:", error);
        // On error, assume token is valid to prevent unnecessary logouts
        setIsValidSession(true);
      }

      setIsLoading(false);
    }

    validateToken();
  }, [token]);

  // Start the periodic check only after initial validation
  useEffect(() => {
    if (!isLoading && isValidSession && token) {
      loginCheck();
    }
  }, [isLoading, isValidSession, token]);

  // Show loading indicator while validating token
  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '100vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="sr-only">Validating session...</span>
        </div>
      </div>
    );
  }

  // If no token or invalid session, show login
  if (!token || !isValidSession) {
      return (
    <Router>
      <ScrollToTop />
      <Routes>
      <Route path="/accept-invitation" element={<AcceptInvitation />} />
        <Route path="/register" element={<Register setToken={setToken} />}>
        </Route>
        <Route path="/*" element={<Login setToken={setToken}/>}>
        </Route>
      </Routes>
    </Router>
  );
  }

  return (
    <Router>
      <ScrollToTop />
      <Routes>
        <Route path="/logout" element={<Logout />}>
	</Route>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="/dedicatedorder" element={<DedicatedOrder />} />
          <Route path="/register" element={<LoginRedirect />} />
          <Route path="/login" element={<LoginRedirect />} />
          <Route path="/cloudorder" element={<CloudOrder />} />
          <Route path="/colocationorder" element={<ColocationOrder />} />
          <Route path="/colocationassetsorder" element={<ColocationAssetsOrder />} />
          <Route path="/iptransitorder" element={<IPTransitOrder />} />
          <Route path="/reseller" element={<Reseller />}/>
          <Route path="/reseller/:section" element={<Reseller />} />
          <Route path="/faq" element={<Faq />}/>
          <Route path="billing/:section" element={<Billing />} />
          <Route path="billing/invoices/:id" element={<InvoiceDetails />} />  {/* New route for invoice details */}
          <Route path="billing/history/:id" element={<TransactionDetails />} />  {/* New route for transaction details */}
          <Route path="/support" element={<Support />}/>
          <Route path="/support/newcase" element={<NewTicket />}/>
          <Route path="/support/:id" element={<Ticket />}/>
          <Route path="/account" element={<Account />}/>
          <Route path="/account/:id" element={<Account />}/>
          <Route path="/*" element={<NoPage />} />
          <Route path="/services/:id" element={<ServerDetailsView />} />
          <Route path="/vps-services/:id" element={<VpsServerDetailsView />} />
          <Route path="/cloud-services/:id" element={<CloudServerDetailsView />} />
          <Route path="/ip-transit-services/:id" element={<IPTransitDetailsView />} />
          <Route path="/colocation-services/:id" element={<ColocationDetailsView />} />
          <Route path="/colocation-assets-services/:id" element={<ColocationAssetsDetailsView />} />
          <Route path="/logincheck" element={<LoginChecker />} />
        </Route>
      </Routes>
    </Router>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<MainApp />);