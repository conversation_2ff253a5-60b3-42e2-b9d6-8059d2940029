import React from 'react';
import { library } from '@fortawesome/fontawesome-svg-core';
import { faMemory, faMicrochip, faServer, faBolt, faHdd, faWifi } from '@fortawesome/free-solid-svg-icons';
import logo from './logo.svg';
import './App.css';

// Add icons to the library
library.add(faMemory, faMicrochip, faServer, faBolt, faHdd, faWifi);



// Make sure to return a valid React component
function App() {
  return (
    
    <div className="App">
      
      <header className="App-header">
        <img src={logo} className="App-logo" alt="logo" />
        <p>
          Edit <code>src/App.js</code> and save to reload.
        </p>
        <a
          className="App-link"
          href="https://reactjs.org"
          target="_blank"
          rel="noopener noreferrer"
        >
          Learn React
        </a>
      </header>
    </div>
  );
}

// Make sure to export the component correctly
export default App;