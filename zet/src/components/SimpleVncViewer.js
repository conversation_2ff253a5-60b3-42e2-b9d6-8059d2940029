import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

/**
 * A simple VNC viewer component that uses an iframe to display the VNC console
 */
const SimpleVncViewer = (props) => {
  const { vncDetails, onConnect, onError } = props;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Simulate connection process
    const timer = setTimeout(() => {
      setLoading(false);
      if (onConnect) onConnect();
    }, 1500);

    return () => clearTimeout(timer);
  }, [onConnect]);

  const handleIframeError = () => {
    setError('Failed to load VNC console');
    if (onError) onError(new Error('Failed to load VNC console'));
  };

  // Safely extract values from vncDetails
  const fallbackUrl = vncDetails && vncDetails.fallback_url ? vncDetails.fallback_url : null;
  const vncUrl = vncDetails && vncDetails.vnc_url ? vncDetails.vnc_url : null;
  const serverId = vncDetails && vncDetails.server_id ? vncDetails.server_id : '';

  // For direct VNC connection (if we implement noVNC in the future)
  const vncHost = vncDetails && vncDetails.vnc_host ? vncDetails.vnc_host : null;
  const vncPort = vncDetails && vncDetails.vnc_port ? vncDetails.vnc_port : null;
  const vncPassword = vncDetails && vncDetails.vnc_password ? vncDetails.vnc_password : '';

  // Use the VNC URL from the API if available, otherwise fall back to the direct SolusVM console URL
  const consoleUrl = vncUrl || `https://virt.zetservers.com/console.php?vserverid=${serverId}`;

  console.log('VNC Details:', {
    fallbackUrl,
    vncUrl,
    vncHost,
    vncPort,
    hasPassword: !!vncPassword,
    consoleUrl,
    usingDirectConsoleUrl: !vncUrl
  });



  return (
    <div className="vnc-container" style={{ width: '100%', height: '100%', position: 'relative' }}>
      {loading && (
        <div className="position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-light">
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
        
            </div>
            <p className="mt-2">Loading VNC console...</p>
          </div>
        </div>
      )}
      <iframe
        src={consoleUrl}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          display: loading ? 'none' : 'block'
        }}
        title="VNC Console"
        onError={handleIframeError}
        onLoad={() => setLoading(false)}
        allow="fullscreen; clipboard-read; clipboard-write"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation"
        referrerPolicy="no-referrer"
      />
    </div>
  );
};

SimpleVncViewer.propTypes = {
  vncDetails: PropTypes.object.isRequired,
  onConnect: PropTypes.func,
  onError: PropTypes.func
};

SimpleVncViewer.defaultProps = {
  onConnect: () => {},
  onError: () => {}
};

export default SimpleVncViewer;
