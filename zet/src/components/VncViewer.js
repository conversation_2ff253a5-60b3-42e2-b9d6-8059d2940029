import React, { useEffect, useRef } from 'react';
// Import RFB dynamically to avoid top-level await issues
let RFB;
try {
  // Try to import synchronously first
  RFB = require('@novnc/novnc/core/rfb').default;
} catch (e) {
  console.warn('Failed to import RFB synchronously:', e);
  // Fallback to a placeholder that will be replaced in useEffect
  RFB = null;
}
import PropTypes from 'prop-types';

const VncViewer = ({ host, port, path, password, onConnect, onDisconnect, onError }) => {
  const canvasRef = useRef(null);
  const rfbRef = useRef(null);

  useEffect(() => {
    // Clean up function to disconnect VNC when component unmounts
    return () => {
      if (rfbRef.current) {
        rfbRef.current.disconnect();
        rfbRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    const initConnection = async () => {
      if (!canvasRef.current) return;

      // Disconnect existing connection if any
      if (rfbRef.current) {
        rfbRef.current.disconnect();
        rfbRef.current = null;
      }

      if (!host) return;

      try {
        // If RFB is not available, try to import it dynamically
        if (!RFB) {
          try {
            // Try to import from different paths
            const novnc = await import('@novnc/novnc/core/rfb');
            RFB = novnc.default || novnc.RFB;

            if (!RFB) {
              throw new Error('Failed to import RFB from @novnc/novnc/core/rfb');
            }
          } catch (importError) {
            console.error('Failed to import RFB dynamically:', importError);
            if (onError) onError(new Error('Failed to load VNC client library'));
            return;
          }
        }

        // Construct the WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${host}:${port}/${path}`;

        console.log('Connecting to VNC server at:', wsUrl);

        // Create a new RFB connection
        rfbRef.current = new RFB(canvasRef.current, wsUrl, {
          credentials: { password },
          shared: true,
          repeaterID: '',
          wsProtocols: ['binary'],
        });

        // Set up event handlers
        rfbRef.current.addEventListener('connect', () => {
          console.log('Connected to VNC server');
          if (onConnect) onConnect();
        });

        rfbRef.current.addEventListener('disconnect', (e) => {
          console.log('Disconnected from VNC server:', e);
          if (onDisconnect) onDisconnect(e);
        });

        rfbRef.current.addEventListener('credentialsrequired', () => {
          console.log('VNC credentials required');
          rfbRef.current.sendCredentials({ password });
        });
      } catch (err) {
        console.error('Error connecting to VNC server:', err);
        if (onError) onError(err);
      }
    };

    initConnection();
  }, [host, port, path, password, onConnect, onDisconnect, onError]);

  return (
    <div className="vnc-container" style={{ width: '100%', height: '100%' }}>
      <div ref={canvasRef} style={{ width: '100%', height: '100%' }}></div>
    </div>
  );
};

VncViewer.propTypes = {
  host: PropTypes.string.isRequired,
  port: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  path: PropTypes.string,
  password: PropTypes.string,
  onConnect: PropTypes.func,
  onDisconnect: PropTypes.func,
  onError: PropTypes.func
};

VncViewer.defaultProps = {
  path: '',
  password: '',
  onConnect: () => {},
  onDisconnect: () => {},
  onError: () => {}
};

export default VncViewer;
