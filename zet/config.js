// src/config.js
// Configuration file for the application

/**
 * Application configuration
 */
const config = {
  // API domain URL - default value
  apiDomain: 'http://82.152.132.28',

  // API base path
  apiBasePath: '/New_client',

  // Full API base URL
  get apiBaseUrl() {
    return `${this.apiDomain}${this.apiBasePath}`;
  }
};

// Function to fetch API domain from server
const fetchApiDomain = async () => {
  try {
    // Use the current domain to fetch the API domain
    const currentDomain = window.location.origin;
    console.log('Fetching API domain from:', `${currentDomain}/get_api_domain.php`);

    const response = await fetch(`${currentDomain}/get_api_domain.php`);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('API domain response:', responseText);

    try {
      const data = JSON.parse(responseText);
      if (data.success && data.api_domain) {
        config.apiDomain = data.api_domain;
        console.log('API domain updated:', config.apiDomain);
      }
    } catch (parseError) {
      console.error('Failed to parse API domain response:', parseError);
    }
  } catch (error) {
    console.error('Failed to fetch API domain:', error);
    // Fallback to direct IP if fetch fails
    console.log('Using fallback API domain');
  }
};

// Try to fetch the API domain when the config is loaded
fetchApiDomain();

// Export the API URL for use in components
export const API_URL = config.apiBaseUrl;

// Export a function to get the latest API URL
export const getApiUrl = () => config.apiBaseUrl;

export default config;