{"name": "zet", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.0", "mdb-react-ui-kit": "^6.0.0", "micromodal": "^0.4.10", "primeflex": "^3.3.0", "primereact": "^9.3.0", "react": "^18.2.0", "react-bootstrap": "^2.7.4", "react-data-table-component": "^7.5.3", "react-dom": "^18.2.0", "react-scripts": "^5.0.1", "reactstrap": "^9.2.3", "recharts": "^2.15.1", "styled-components": "^5.3.10", "web-vitals": "^2.1.4"}, "scripts": {"start": "CHOKIDAR_USEPOLLING=true WDS_SOCKET_HOST=0 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"cors": "^2.8.5", "express": "^4.18.2", "react-router-dom": "^6.10.0"}}