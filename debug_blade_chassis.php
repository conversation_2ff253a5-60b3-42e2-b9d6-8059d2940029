<?php
require_once 'config.php';

// Debug the specific blade server you mentioned
$blade_id = 245;
$chassis_id = 48;
$cpu_id = 2;

echo "=== Debugging Blade Server $blade_id ===\n\n";

try {
    // Check the blade server data
    $blade_check = $pdo->prepare("
        SELECT bsi.*, ich.country_id as chassis_country_id, ich.city_id as chassis_city_id,
               c.city as city_name, co.country as country_name
        FROM blade_server_inventory bsi
        LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
        LEFT JOIN cities c ON ich.city_id = c.id
        LEFT JOIN countries co ON ich.country_id = co.id
        WHERE bsi.id = ?
    ");
    $blade_check->execute([$blade_id]);
    $blade_data = $blade_check->fetch(PDO::FETCH_ASSOC);
    
    if ($blade_data) {
        echo "Blade Server Data:\n";
        echo "  ID: {$blade_data['id']}\n";
        echo "  Status: {$blade_data['status']}\n";
        echo "  CPU: {$blade_data['cpu']}\n";
        echo "  Chassis ID: {$blade_data['chassis_id']}\n";
        echo "  Order ID: " . ($blade_data['order_id'] ?? 'NULL') . "\n";
        echo "  Chassis Country ID: " . ($blade_data['chassis_country_id'] ?? 'NULL') . "\n";
        echo "  Chassis City ID: " . ($blade_data['chassis_city_id'] ?? 'NULL') . "\n";
        echo "  City Name: " . ($blade_data['city_name'] ?? 'NULL') . "\n";
        echo "  Country Name: " . ($blade_data['country_name'] ?? 'NULL') . "\n\n";
    } else {
        echo "Blade server $blade_id not found!\n\n";
    }
    
    // Check chassis data separately
    echo "=== Chassis $chassis_id Data ===\n";
    $chassis_check = $pdo->prepare("
        SELECT ich.*, c.city as city_name, co.country as country_name
        FROM inventory_chassis ich
        LEFT JOIN cities c ON ich.city_id = c.id
        LEFT JOIN countries co ON ich.country_id = co.id
        WHERE ich.id = ?
    ");
    $chassis_check->execute([$chassis_id]);
    $chassis_data = $chassis_check->fetch(PDO::FETCH_ASSOC);
    
    if ($chassis_data) {
        echo "Chassis Data:\n";
        echo "  ID: {$chassis_data['id']}\n";
        echo "  Label: {$chassis_data['label']}\n";
        echo "  Status: {$chassis_data['status']}\n";
        echo "  Country ID: " . ($chassis_data['country_id'] ?? 'NULL') . "\n";
        echo "  City ID: " . ($chassis_data['city_id'] ?? 'NULL') . "\n";
        echo "  City Name: " . ($chassis_data['city_name'] ?? 'NULL') . "\n";
        echo "  Country Name: " . ($chassis_data['country_name'] ?? 'NULL') . "\n\n";
    } else {
        echo "Chassis $chassis_id not found!\n\n";
    }
    
    // Check all available cities/countries
    echo "=== Available Cities/Countries ===\n";
    $cities_check = $pdo->prepare("
        SELECT c.id, c.city, c.country_id, co.country, co.flag_code
        FROM cities c
        LEFT JOIN countries co ON c.country_id = co.id
        ORDER BY co.country, c.city
    ");
    $cities_check->execute();
    while ($city = $cities_check->fetch(PDO::FETCH_ASSOC)) {
        echo "  City ID: {$city['id']}, City: {$city['city']}, Country ID: {$city['country_id']}, Country: {$city['country']}, Flag: {$city['flag_code']}\n";
    }
    
    echo "\n=== Test Query Results ===\n";
    
    // Test the exact query used in the API for different country IDs
    $test_countries = [1, 2, 3]; // Test common country IDs
    
    foreach ($test_countries as $country_id) {
        $test_query = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM blade_server_inventory bsi
            LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
            WHERE bsi.status = 'Available'
            AND ich.country_id = ?
            AND bsi.order_id IS NULL
            AND bsi.cpu = ?
        ");
        $test_query->execute([$country_id, $cpu_id]);
        $result = $test_query->fetch(PDO::FETCH_ASSOC);
        
        echo "Country ID $country_id, CPU $cpu_id: {$result['count']} blade servers\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
