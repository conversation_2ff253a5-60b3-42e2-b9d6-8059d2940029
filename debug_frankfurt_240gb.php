<?php
// Debug Frankfurt location with 240GB SSD
require_once 'mysql.php';

// Include the calculateServerAvailability function
require_once 'api.php';

echo "=== Debugging Frankfurt Location with 240GB SSD ===\n\n";

try {
    $cpu_id = 1; // Dual Intel Xeon E5-2630v3
    $storage_id = 1; // 240GB SSD
    $bandwidth_id = 1; // 1 Gbps
    
    // Test Bucharest (should have servers)
    echo "=== Testing Bucharest (country_id=1, city_id=1) ===\n";
    $bucharest_result = calculateServerAvailability($pdo, $cpu_id, $storage_id, $bandwidth_id, 1, 1);
    echo "Bucharest result:\n";
    print_r($bucharest_result);
    
    // Test Frankfurt (should have no servers)
    echo "\n=== Testing Frankfurt (country_id=2, city_id=2) ===\n";
    $frankfurt_result = calculateServerAvailability($pdo, $cpu_id, $storage_id, $bandwidth_id, 2, 2);
    echo "Frankfurt result:\n";
    print_r($frankfurt_result);
    
    echo "\n=== Analysis ===\n";
    echo "Bucharest:\n";
    echo "- Available count: {$bucharest_result['available_count']}\n";
    echo "- Stock color: {$bucharest_result['stock_color']}\n";
    echo "- Has stock: " . ($bucharest_result['has_stock'] ? 'true' : 'false') . "\n";
    echo "- Delivery time: {$bucharest_result['delivery_time']}\n";
    echo "- Stock message: {$bucharest_result['stock_message']}\n";
    
    echo "\nFrankfurt:\n";
    echo "- Available count: {$frankfurt_result['available_count']}\n";
    echo "- Stock color: {$frankfurt_result['stock_color']}\n";
    echo "- Has stock: " . ($frankfurt_result['has_stock'] ? 'true' : 'false') . "\n";
    echo "- Delivery time: {$frankfurt_result['delivery_time']}\n";
    echo "- Stock message: {$frankfurt_result['stock_message']}\n";
    
    if ($frankfurt_result['available_count'] > 0 || $frankfurt_result['has_stock']) {
        echo "\n❌ PROBLEM: Frankfurt is showing as available when it shouldn't be!\n";
        echo "This explains why Frankfurt appears in the UI.\n";
    } else {
        echo "\n✅ Frankfurt correctly shows as unavailable.\n";
        echo "The issue might be elsewhere in the location processing logic.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
