<?php
// Test script to verify the flag fetching logic for cloud services
require_once("mysql.php");

echo "=== Testing Flag Fetching for Cloud Services ===\n\n";

// Test the updated query to see if it properly joins the tables
$test_query = "
    SELECT
        oi.id,
        oi.hostname as label,
        oi.type,
        oi.location_id,
        -- Dedicated server location data
        c.city as dedicated_city,
        co.country as dedicated_country_name,
        co.flag_code as dedicated_flag_code,
        -- VPS location data for cloud services
        vl.city as vps_city,
        vco.country as vps_country_name,
        vco.flag_code as vps_flag_code
    FROM orders_items oi
    LEFT JOIN cities c ON oi.location_id = c.id
    LEFT JOIN countries co ON c.country_id = co.id
    -- Join VPS locations for cloud services
    LEFT JOIN vps_locations vl ON oi.location_id = vl.solus_location_id
    LEFT JOIN countries vco ON vl.country_id = vco.id
    WHERE oi.type = 'Cloud' OR oi.type LIKE '%vps%' OR oi.type LIKE '%cloud%'
    LIMIT 10
";

try {
    $stmt = $pdo->prepare($test_query);
    $stmt->execute();
    
    echo "Found " . $stmt->rowCount() . " cloud services:\n\n";
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "Service ID: {$row['id']}\n";
        echo "  Label: {$row['label']}\n";
        echo "  Type: {$row['type']}\n";
        echo "  Location ID: {$row['location_id']}\n";
        echo "  Dedicated City: " . ($row['dedicated_city'] ?: 'NULL') . "\n";
        echo "  Dedicated Country: " . ($row['dedicated_country_name'] ?: 'NULL') . "\n";
        echo "  Dedicated Flag: " . ($row['dedicated_flag_code'] ?: 'NULL') . "\n";
        echo "  VPS City: " . ($row['vps_city'] ?: 'NULL') . "\n";
        echo "  VPS Country: " . ($row['vps_country_name'] ?: 'NULL') . "\n";
        echo "  VPS Flag: " . ($row['vps_flag_code'] ?: 'NULL') . "\n";
        
        // Test the logic
        $is_cloud_service = (strtolower($row['type']) === 'cloud');
        $location_short = "";
        
        if ($is_cloud_service) {
            if (!empty($row['vps_flag_code'])) {
                $location_short = strtolower($row['vps_flag_code']);
            } elseif (!empty($row['vps_country_name'])) {
                $location_short = strtolower(substr($row['vps_country_name'], 0, 2));
            }
        } else {
            if (!empty($row['dedicated_flag_code'])) {
                $location_short = strtolower($row['dedicated_flag_code']);
            } elseif (!empty($row['dedicated_country_name'])) {
                $location_short = strtolower(substr($row['dedicated_country_name'], 0, 2));
            }
        }
        
        if (empty($location_short)) {
            $location_short = "ro"; // Default
        }
        
        echo "  Final Flag: $location_short\n";
        echo "  Is Cloud Service: " . ($is_cloud_service ? 'YES' : 'NO') . "\n";
        echo "  ---\n\n";
    }
    
    // Also test the vps_locations table structure
    echo "\n=== VPS Locations Table Structure ===\n";
    $structure_query = "DESCRIBE vps_locations";
    $structure_stmt = $pdo->query($structure_query);
    while ($column = $structure_stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "{$column['Field']} - {$column['Type']}\n";
    }
    
    echo "\n=== Sample VPS Locations Data ===\n";
    $sample_query = "SELECT * FROM vps_locations LIMIT 5";
    $sample_stmt = $pdo->query($sample_query);
    while ($location = $sample_stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "ID: {$location['id']}, City: {$location['city']}, Country ID: {$location['country_id']}, Solus Location ID: " . ($location['solus_location_id'] ?? 'NULL') . "\n";
    }
    
    echo "\n=== Countries Table Structure ===\n";
    $countries_structure = "DESCRIBE countries";
    $countries_stmt = $pdo->query($countries_structure);
    while ($column = $countries_stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "{$column['Field']} - {$column['Type']}\n";
    }
    
    echo "\n=== Sample Countries Data ===\n";
    $countries_query = "SELECT * FROM countries LIMIT 5";
    $countries_sample = $pdo->query($countries_query);
    while ($country = $countries_sample->fetch(PDO::FETCH_ASSOC)) {
        echo "ID: {$country['id']}, Country: {$country['country']}, Flag Code: " . ($country['flag_code'] ?? 'NULL') . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
