<!DOCTYPE html>
<html>
<head>
    <title>Test Simple Message</title>
</head>
<body>
    <h1>Test Simple Message (No Files)</h1>
    
    <form id="messageForm">
        <div>
            <label>Ticket ID:</label>
            <input type="number" id="ticketId" required value="1">
        </div>
        <div>
            <label>Message:</label>
            <textarea id="message" required placeholder="Test message">Test message from browser</textarea>
        </div>
        <div>
            <label>Token:</label>
            <input type="text" id="token" required placeholder="Your authentication token">
        </div>
        <button type="submit">Send Message (JSON)</button>
        <button type="button" id="sendFormData">Send Message (FormData)</button>
    </form>
    
    <div id="result"></div>
    
    <script>
    // Test JSON request (original way)
    document.getElementById('messageForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const data = {
            ticket_id: document.getElementById('ticketId').value,
            message: document.getElementById('message').value,
            token: document.getElementById('token').value
        };
        
        fetch('/api.php?f=add_message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = '<h3>JSON Result:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('result').innerHTML = '<p style="color: red;">JSON Error: ' + error + '</p>';
        });
    });
    
    // Test FormData request (new way)
    document.getElementById('sendFormData').addEventListener('click', function() {
        const formData = new FormData();
        formData.append('ticket_id', document.getElementById('ticketId').value);
        formData.append('message', document.getElementById('message').value);
        formData.append('token', document.getElementById('token').value);
        
        fetch('/api.php?f=add_message', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = '<h3>FormData Result:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('result').innerHTML = '<p style="color: red;">FormData Error: ' + error + '</p>';
        });
    });
    </script>
</body>
</html> 