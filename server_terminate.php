<?php
// Start output buffering to prevent any output before our JSON response
ob_start();

// Set headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Log the start of the API request
error_log("Server Terminate API Request: " . $_SERVER['REQUEST_METHOD']);

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'error' => 1,
        'message' => 'Method not allowed. Only POST requests are accepted.'
    ]);
    exit;
}

// Include database connection
require_once("mysql.php");

// Get request data
$input_data = file_get_contents('php://input');
error_log("Raw input data: " . $input_data);

// Helper function to get JSON error message
function json_last_encode_error_msg() {
    switch (json_last_error()) {
        case JSON_ERROR_NONE:
            return 'No error';
        case JSON_ERROR_DEPTH:
            return 'Maximum stack depth exceeded';
        case JSON_ERROR_STATE_MISMATCH:
            return 'Underflow or the modes mismatch';
        case JSON_ERROR_CTRL_CHAR:
            return 'Unexpected control character found';
        case JSON_ERROR_SYNTAX:
            return 'Syntax error, malformed JSON';
        case JSON_ERROR_UTF8:
            return 'Malformed UTF-8 characters, possibly incorrectly encoded';
        default:
            return 'Unknown error';
    }
}

$data = json_decode($input_data, true);
if (!$data) {
    error_log("Failed to parse JSON input: " . json_last_encode_error_msg());
    echo json_encode([
        'error' => 1,
        'message' => 'Invalid JSON input',
        'details' => json_last_error_msg()
    ]);
    exit;
}

error_log("Parsed input data: " . print_r($data, true));

// Verify token
if (!isset($data['token']) || empty($data['token'])) {
    error_log("No token provided");
    echo json_encode([
        'error' => 1,
        'message' => 'Authentication required: No token provided'
    ]);
    exit;
}

// Verify required parameters
if (!isset($data['server_id']) || !isset($data['timing']) || !isset($data['reason'])) {
    error_log("Missing required parameters");
    echo json_encode([
        'error' => 1,
        'message' => 'Missing required parameters',
        'details' => 'server_id, timing, and reason are required'
    ]);
    exit;
}

$server_id = $data['server_id'];
$timing = $data['timing']; // 'immediately' or 'end-of-billing'
$reason = $data['reason'];
$token = $data['token'];

// Authenticate user
try {
    // Include the authentication handler
    require_once("auth_handler.php");

    // Create authentication handler
    $auth = new Auth($pdo);

    // Verify token
    $user = $auth->verifyToken($token);
    if (!$user) {
        error_log("Invalid token: " . substr($token, 0, 10) . "...");
        echo json_encode([
            'error' => 1,
            'message' => 'Authentication failed: Invalid token'
        ]);
        exit;
    }

    $user_id = $user['id'];
    error_log("User authenticated: ID=$user_id");

    // Check if termination_reason column exists in orders_items table
    $columnCheck = $pdo->prepare("SHOW COLUMNS FROM orders_items LIKE 'termination_reason'");
    $columnCheck->execute();
    if($columnCheck->rowCount() == 0) {
        // Add termination_reason column if it doesn't exist
        $pdo->exec("ALTER TABLE orders_items ADD COLUMN termination_reason TEXT DEFAULT NULL");
        error_log("Added missing termination_reason column to orders_items table");
    }

    // Get server details
    try {
        // Check if this is an inventory dedicated server (ID >= 1000000)
        if ($server_id >= 1000000) {
            error_log("Using inventory_dedicated_servers table for server ID: $server_id");
            $sth = $pdo->prepare("
                SELECT ids.*, o.owner_id, o.id as order_id, oi.user_id as order_user_id,
                       ids.switch_id, ids.port1, ids.port2, ids.port3, ids.port4
                FROM inventory_dedicated_servers ids
                LEFT JOIN orders o ON ids.order_id = o.id
                LEFT JOIN orders_items oi ON oi.server_id = ids.id
                WHERE ids.id = :server_id
            ");
        } else {
            error_log("Using blade_server_inventory table for server ID: $server_id");
            // Blade server
            $sth = $pdo->prepare("
                SELECT bsi.*, o.owner_id, o.id as order_id, oi.user_id as order_user_id,
                       bsi.switch_id, bsi.port1, bsi.port2, bsi.port3, bsi.port4
                FROM blade_server_inventory bsi
                LEFT JOIN orders o ON bsi.order_id = o.id
                LEFT JOIN orders_items oi ON oi.server_id = bsi.id
                WHERE bsi.id = :server_id
            ");
        }

        $sth->bindValue(':server_id', $server_id);
        $sth->execute();
        $server = $sth->fetch(PDO::FETCH_ASSOC);

        // Log server data
        error_log("Server data fetched: " . ($server ? "Found" : "Not found") .
            ", Row count: " . $sth->rowCount());

        if (!$server) {
            error_log("Server not found for ID: $server_id");
            echo json_encode([
                'error' => 1,
                'message' => 'Server not found'
            ]);
            exit;
        }

        // Debug: Log all server fields to see what's available
        error_log("All server fields: " . print_r($server, true));

        // Check if the user has access to this server
        $owner_id = $server['owner_id'] ?? null;
        $order_user_id = $server['order_user_id'] ?? null;
        $order_id = $server['order_id'] ?? null;

        error_log("Ownership check - User ID: $user_id, Server owner_id: " .
            ($owner_id ?? 'null') . ", Order user_id: " . ($order_user_id ?? 'null'));

        // Check if the user is either the owner or the order user
        $has_access = ($owner_id == $user_id) || ($order_user_id == $user_id);

        if (!$has_access) {
            error_log("Access denied - user $user_id trying to access server owned by " .
                ($owner_id ?? 'null') . " with order user " . ($order_user_id ?? 'null'));
            echo json_encode([
                'error' => 1,
                'message' => 'Access denied - you do not own this server',
                'details' => 'You must be the owner or the order user to perform this action'
            ]);
            exit;
        } else {
            error_log("Access granted - user $user_id has access to server $server_id");
        }

        // Begin transaction
        $pdo->beginTransaction();

        try {
            // 1. Update status in orders_items table to 'terminated' or 'pending_termination' based on timing
            $new_status = ($timing === 'immediately') ? 'Terminated' : 'Pending_Termination';

            // Update orders_items table only
            if ($timing === 'immediately') {
                // For immediate termination, clear the server_id
                $update_items = $pdo->prepare("
                    UPDATE orders_items
                    SET status = :status,
                        termination_reason = :reason,
                        server_id = NULL
                    WHERE server_id = :server_id
                ");
            } else {
                // For end-of-billing termination, keep the server_id
                $update_items = $pdo->prepare("
                    UPDATE orders_items
                    SET status = :status,
                        termination_reason = :reason
                    WHERE server_id = :server_id
                ");
            }
            $update_items->bindValue(':status', $new_status);
            $update_items->bindValue(':reason', $reason);
            $update_items->bindValue(':server_id', $server_id);
            $update_items->execute();

            error_log("Updated orders_items status to $new_status for server ID: $server_id");

            // 2. Update server status
            $server_table = ($server_id >= 1000000) ? 'inventory_dedicated_servers' : 'blade_server_inventory';
            $update_server = $pdo->prepare("
                UPDATE $server_table
                SET status = :status
                WHERE id = :server_id
            ");
            $update_server->bindValue(':status', $new_status);
            $update_server->bindValue(':server_id', $server_id);
            $update_server->execute();

            error_log("Updated server status to $new_status for server ID: $server_id in table $server_table");

            // 3. Log the termination request
            $log_sql = "
                INSERT INTO activity_log
                (user_id, action, description, user_name, activity_type)
                VALUES
                (:user_id, :action, :description, :user_name, 'server_management')
            ";

            $log_stmt = $pdo->prepare($log_sql);
            $log_stmt->bindValue(':user_id', $user_id);
            $log_stmt->bindValue(':action', 'server_terminate');
            $log_stmt->bindValue(':description', "Server {$server_id} termination requested. Timing: {$timing}. Reason: {$reason}");
            $log_stmt->bindValue(':user_name', 'User ' . $user_id);
            $log_stmt->execute();

            error_log("Logged termination request in activity_log");

            // 4. If immediate termination, power off the server
            if ($timing === 'immediately') {
                // Get IPMI details
                $ipmi_ip = $server['ipmi'];
                $ipmi_user = 'root'; // Default user
                $ipmi_pass = $server['ipmi_root_pass'] ?: $server['ipmi_user_pass'];

                if ($ipmi_ip && $ipmi_pass) {
                    // Power off the server using IPMI
                    $power_command = "ipmitool -I lanplus -H $ipmi_ip -U $ipmi_user -P '$ipmi_pass' power off";
                    $safe_command = preg_replace('/-P \'.*?\'/', '-P \'[REDACTED]\'', $power_command);
                    error_log("Executing IPMI power off command: $safe_command");

                    exec($power_command, $power_output, $power_status);
                    error_log("Power off command result - Status: $power_status, Output: " . implode("\n", $power_output));

                    // 5. Shutdown switch port using SNMP for Arista switches
                    $switch_id = $server['switch_id'];
                    $port1 = $server['port1'];

                    if ($switch_id && $port1) {
                        // Get switch details
                        $switch_query = $pdo->prepare("
                            SELECT switch_ip, snmp_community
                            FROM inventory_switches
                            WHERE id = :switch_id
                        ");
                        $switch_query->bindValue(':switch_id', $switch_id);
                        $switch_query->execute();
                        $switch = $switch_query->fetch(PDO::FETCH_ASSOC);

                        if ($switch && $switch['switch_ip'] && $switch['snmp_community']) {
                            // Shutdown port using SNMP
                            $switch_ip = $switch['switch_ip'];
                            $community = $switch['snmp_community'];

                            // For Arista switches, use SNMP to shutdown the port
                            // OID for interface admin status: .*******.*******.1.7.{ifIndex}
                            // Value 2 = down
                            $snmp_command = "snmpset -v2c -c $community $switch_ip .*******.*******.1.7.$port1 i 2";
                            error_log("Executing SNMP command to shutdown port: $snmp_command");

                            exec($snmp_command, $snmp_output, $snmp_status);
                            error_log("SNMP command result - Status: $snmp_status, Output: " . implode("\n", $snmp_output));

                            // Update port status in database
                            $port_update = $pdo->prepare("
                                UPDATE inventory_switch_ports
                                SET status = 'Disabled'
                                WHERE switch_id = :switch_id AND port_number = :port_number
                            ");
                            $port_update->bindValue(':switch_id', $switch_id);
                            $port_update->bindValue(':port_number', $port1);
                            $port_update->execute();

                            error_log("Updated port status to Disabled for switch ID: $switch_id, port: $port1");
                        } else {
                            error_log("Switch details not found or incomplete for switch ID: $switch_id");
                        }
                    } else {
                        error_log("No switch ID or port found for server $server_id");
                    }
                } else {
                    error_log("IPMI details not available for server $server_id");
                }
            }

            // Commit all changes
            $pdo->commit();

            // Return success
            $response = [
                'error' => 0,
                'message' => 'Server termination ' . ($timing === 'immediately' ? 'completed' : 'scheduled'),
                'details' => 'Server has been ' . ($timing === 'immediately' ? 'terminated' : 'scheduled for termination at the end of the billing period'),
                'server_id' => $server_id,
                'timing' => $timing
            ];

            error_log("Sending success response: " . json_encode($response));
            echo json_encode($response);
            exit;

        } catch (Exception $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            error_log("Error in server termination transaction: " . $e->getMessage());
            throw $e;
        }

    } catch (Exception $e) {
        error_log("Error in server details: " . $e->getMessage());
        echo json_encode([
            'error' => 1,
            'message' => 'Server error',
            'details' => $e->getMessage()
        ]);
        exit;
    }

} catch (Exception $e) {
    error_log("Error in authentication: " . $e->getMessage());
    echo json_encode([
        'error' => 1,
        'message' => 'Authentication error',
        'details' => $e->getMessage()
    ]);
    exit;
}
