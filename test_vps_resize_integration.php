<?php
/**
 * Test script to verify VPS resize integration in Solus
 * This script tests the resize_vps_in_solus function
 */

require_once("mysql.php");

// Load the function from api.php
require_once("api.php");

echo "=== VPS Resize Integration Test ===\n\n";

// Test parameters
$test_server_id = 1973; // Example server ID - replace with an actual server ID for testing
$test_plan_id = 2;      // Example plan ID - replace with an actual plan ID for testing

echo "Testing resize_vps_in_solus function...\n";
echo "Server ID: $test_server_id\n";
echo "Plan ID: $test_plan_id\n";
echo "API Endpoint: https://virt.zetservers.com/api/v1/servers/$test_server_id/resize\n\n";

// Test the function
echo "Calling resize_vps_in_solus()...\n";
$result = resize_vps_in_solus($test_server_id, $test_plan_id);

if ($result) {
    echo "✅ SUCCESS: VPS resize API call completed successfully\n";
} else {
    echo "❌ FAILED: VPS resize API call failed\n";
}

echo "\n=== Test Complete ===\n";
echo "Check the error logs for detailed API response information.\n";
echo "Log file location: /tmp/api_debug.log\n";

?> 