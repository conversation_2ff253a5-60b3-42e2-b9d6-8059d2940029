<?php
/**
 * Test API Call for PayPal Vaulting Confirmation
 * This script will call the API endpoint directly to test if it's working
 */

// Get the current setup token from database
require_once("./mysql.php");

$stmt = $pdo->prepare("SELECT * FROM payment_methods WHERE user_id = 1 AND processor = 'paypal' AND status = 'pending' ORDER BY created DESC LIMIT 1");
$stmt->execute();
$paymentMethod = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$paymentMethod) {
    echo "❌ No pending PayPal payment method found. Please create one first.\n";
    exit(1);
}

$setup_token = $paymentMethod['setup_token'];
echo "Testing API with setup token: $setup_token\n\n";

// Prepare the API call
$api_url = 'http://localhost/New_client/api.php?f=confirm_paypal_vaulting';

// Prepare POST data
$post_data = json_encode(['token' => $setup_token]);

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($post_data)
]);

// Add cookies for authentication (you might need to adjust this)
// For testing, we can also try with GET parameters
curl_setopt($ch, CURLOPT_COOKIEFILE, ''); // Enable cookie handling

echo "Making API call to: $api_url\n";
echo "POST data: $post_data\n\n";

// Execute the request
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $http_code\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n\n";

// Also try with GET parameters as fallback
echo "Trying with GET parameters...\n";
$get_url = $api_url . '&token=' . urlencode($setup_token);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $get_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, '');

echo "Making GET call to: $get_url\n";

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $http_code\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n";
?> 