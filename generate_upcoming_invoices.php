<?php
/**
 * <PERSON>ript to generate invoices for services due in 14 days
 * To be run daily at 00:01 via cron
 */

// Include database connection and required functions
$scriptDir = dirname(__FILE__);
require_once($scriptDir . "/mysql.php");
require_once("./email_functions.php");

// Include Stripe library if available
if (file_exists($scriptDir . '/vendor/autoload.php')) {
    require_once($scriptDir . '/vendor/autoload.php');
} elseif (file_exists('./vendor/autoload.php')) {
    require_once('./vendor/autoload.php');
}

// Set error handling - temporarily show errors for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', '/var/log/invoice_generation.log');

// Start script
$start_time = microtime(true);
$log_prefix = "[" . date('Y-m-d H:i:s') . "] ";
error_log($log_prefix . "Starting invoice generation for services due in 14 days");

// Statistics
$stats = [
    'services_checked' => 0,
    'invoices_generated' => 0,
    'automatic_payments_attempted' => 0,
    'automatic_payments_successful' => 0,
    'credit_payments_successful' => 0,
    'stripe_payments_successful' => 0,
    'paypal_payments_successful' => 0,
    'errors' => 0
];

/**
 * Function to attempt automatic Stripe payment for an invoice
 */
function attemptStripePayment($pdo, $invoice_id, $user_id, $invoice_amount, $log_prefix) {
    try {
        error_log($log_prefix . "Attempting Stripe payment for invoice $invoice_id, user $user_id, amount $invoice_amount");

        // Check if user has a default Stripe payment method stored
        $stripeQuery = "SELECT * FROM payment_methods 
                        WHERE user_id = :user_id 
                        AND processor = 'stripe' 
                        AND status = 'active' 
                        AND is_default = 1 
                        ORDER BY created DESC 
                        LIMIT 1";
        $stripeStmt = $pdo->prepare($stripeQuery);
        $stripeStmt->bindValue(":user_id", $user_id);
        $stripeStmt->execute();
        $stripeMethod = $stripeStmt->fetch(PDO::FETCH_ASSOC);

        if (!$stripeMethod) {
            error_log($log_prefix . "No default Stripe payment method found for user $user_id");
            return false;
        }

        // Initialize Stripe
        if (!class_exists('\Stripe\Stripe')) {
            error_log($log_prefix . "Stripe library not available for invoice $invoice_id");
            return false;
        }
        
        if (!defined('STRIPE_SECRET_KEY')) {
            define('STRIPE_SECRET_KEY', 'sk_test_51RR8eZQR2z1KpIsl2jb8CYDtLmuDTbEJzq801T4D40Mga3uPVCc22lGHigIqrsQw8tl9bBA2J59EsAOAU29zmZlD00Zbz5zgqn');
        }
        
        \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

        // First, verify that the payment method still exists in Stripe
        try {
            $paymentMethod = \Stripe\PaymentMethod::retrieve($stripeMethod['payment_method_id']);
            error_log($log_prefix . "Payment method {$stripeMethod['payment_method_id']} verified in Stripe");
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            error_log($log_prefix . "Payment method {$stripeMethod['payment_method_id']} not found in Stripe: " . $e->getMessage());
            
            // Mark the payment method as inactive in our database
            $markInactiveStmt = $pdo->prepare("UPDATE payment_methods SET status = 'inactive' WHERE id = :id");
            $markInactiveStmt->bindValue(":id", $stripeMethod['id']);
            $markInactiveStmt->execute();
            
            error_log($log_prefix . "Marked payment method ID {$stripeMethod['id']} as inactive in database");
            
            // Try to find another active Stripe payment method for this user
            $altStripeQuery = "SELECT * FROM payment_methods 
                              WHERE user_id = :user_id 
                              AND processor = 'stripe' 
                              AND status = 'active' 
                              AND id != :excluded_id
                              ORDER BY created DESC 
                              LIMIT 1";
            $altStripeStmt = $pdo->prepare($altStripeQuery);
            $altStripeStmt->bindValue(":user_id", $user_id);
            $altStripeStmt->bindValue(":excluded_id", $stripeMethod['id']);
            $altStripeStmt->execute();
            $altStripeMethod = $altStripeStmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$altStripeMethod) {
                error_log($log_prefix . "No alternative Stripe payment method found for user $user_id");
                return false;
            }
            
            // Try to verify the alternative payment method
            try {
                $altPaymentMethod = \Stripe\PaymentMethod::retrieve($altStripeMethod['payment_method_id']);
                $stripeMethod = $altStripeMethod; // Use the alternative method
                error_log($log_prefix . "Using alternative payment method {$altStripeMethod['payment_method_id']}");
            } catch (\Stripe\Exception\InvalidRequestException $altE) {
                error_log($log_prefix . "Alternative payment method also invalid: " . $altE->getMessage());
                // Mark this one as inactive too
                $markInactiveStmt->bindValue(":id", $altStripeMethod['id']);
                $markInactiveStmt->execute();
                return false;
            }
        } catch (Exception $e) {
            error_log($log_prefix . "Error verifying payment method: " . $e->getMessage());
            return false;
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Create PaymentIntent with proper off-session parameters
        $amount_cents = round($invoice_amount * 100);
        
        $paymentIntentData = [
            'amount' => $amount_cents,
            'currency' => 'eur',
            'customer' => $stripeMethod['customer_id'],
            'payment_method' => $stripeMethod['payment_method_id'],
            'confirmation_method' => 'automatic',
            'confirm' => true,
            'off_session' => true, // This is key for automatic payments
            'metadata' => [
                'invoice_id' => $invoice_id,
                'user_id' => $user_id,
                'auto_renewal' => 'true'
            ]
        ];

        // Add return_url for some payment methods that might require it
        $paymentIntentData['return_url'] = 'https://client.x-zoneit.ro/billing/invoices/' . $invoice_id;

        $paymentIntent = \Stripe\PaymentIntent::create($paymentIntentData);

        if ($paymentIntent->status === 'succeeded') {
            // Update invoice as paid
            $updateQuery = "UPDATE invoices
                            SET paid = 1,
                                date = NOW(),
                                status = 'Paid'
                            WHERE id = :invoice_id";
            $updateStmt = $pdo->prepare($updateQuery);
            $updateStmt->bindValue(":invoice_id", $invoice_id);
            $updateStmt->execute();

            // Create transaction record
            $txn_id = "ST" . time() . rand(1000, 9999);
            $txnSql = "INSERT INTO invoice_transactions
                (id, invoice_id, user_id, date, type, amount, description, status, processor, payment_intent_id)
                VALUES
                ('$txn_id', '$invoice_id', $user_id, NOW(), 'Auto Stripe Payment', $invoice_amount, 'Automatic renewal payment via Stripe', 'Completed', 'stripe', '{$paymentIntent->id}')";
            $pdo->exec($txnSql);

            // Commit the transaction
            $pdo->commit();
            
            error_log($log_prefix . "Successfully processed Stripe payment for invoice $invoice_id, Payment Intent: {$paymentIntent->id}");
            return true;
            
        } else if ($paymentIntent->status === 'requires_payment_method') {
            $pdo->rollBack();
            error_log($log_prefix . "Stripe payment failed for invoice $invoice_id - requires payment method (card declined or expired)");
            
            // Mark the payment method as inactive since it failed
            $markInactiveStmt = $pdo->prepare("UPDATE payment_methods SET status = 'inactive' WHERE id = :id");
            $markInactiveStmt->bindValue(":id", $stripeMethod['id']);
            $markInactiveStmt->execute();
            error_log($log_prefix . "Marked payment method ID {$stripeMethod['id']} as inactive due to payment failure");
            
            return false;
            
        } else if ($paymentIntent->status === 'requires_action') {
            $pdo->rollBack();
            error_log($log_prefix . "Stripe payment failed for invoice $invoice_id - requires customer action (3D Secure)");
            return false;
            
        } else {
            $pdo->rollBack();
            error_log($log_prefix . "Stripe payment failed for invoice $invoice_id, status: {$paymentIntent->status}");
            return false;
        }

    } catch (\Stripe\Exception\CardException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error_code = $e->getError()->code ?? 'unknown';
        error_log($log_prefix . "Stripe card error ($error_code): " . $e->getMessage());
        
        // Mark card as inactive if it's declined
        if (in_array($error_code, ['card_declined', 'insufficient_funds', 'expired_card'])) {
            $markInactiveStmt = $pdo->prepare("UPDATE payment_methods SET status = 'inactive' WHERE id = :id");
            $markInactiveStmt->bindValue(":id", $stripeMethod['id']);
            $markInactiveStmt->execute();
            error_log($log_prefix . "Marked payment method ID {$stripeMethod['id']} as inactive due to card error: $error_code");
        }
        
        return false;
    } catch (\Stripe\Exception\InvalidRequestException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log($log_prefix . "Stripe invalid request: " . $e->getMessage());
        return false;
    } catch (\Stripe\Exception\AuthenticationException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log($log_prefix . "Stripe authentication error: " . $e->getMessage());
        return false;
    } catch (\Stripe\Exception\ApiConnectionException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log($log_prefix . "Stripe connection error: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log($log_prefix . "Error processing Stripe payment: " . $e->getMessage());
        return false;
    }
}

/**
 * Function to attempt automatic PayPal payment for an invoice
 */
function attemptPayPalPayment($pdo, $invoice_id, $user_id, $invoice_amount, $log_prefix) {
    try {
        error_log($log_prefix . "Attempting PayPal payment for invoice $invoice_id, user $user_id, amount $invoice_amount");

        // Check if user has a default PayPal payment method stored
        $paypalQuery = "SELECT * FROM payment_methods 
                        WHERE user_id = :user_id 
                        AND processor = 'paypal' 
                        AND status = 'active' 
                        AND is_default = 1 
                        ORDER BY created DESC 
                        LIMIT 1";
        $paypalStmt = $pdo->prepare($paypalQuery);
        $paypalStmt->bindValue(":user_id", $user_id);
        $paypalStmt->execute();
        $paypalMethod = $paypalStmt->fetch(PDO::FETCH_ASSOC);

        if (!$paypalMethod) {
            error_log($log_prefix . "No default PayPal payment method found for user $user_id");
            return false;
        }

        // PayPal API credentials
        $paypal_client_id = 'AexATmG6J-fd_YGA3h-xCNBQxf8l1btDDfbfciytmiAemB1Dqc58E8L-1HiKogTEg0jkre8nItHCRv50';
        $paypal_secret = 'EM-vDbIrr0Els7tRqezoR3v2Kk4-b1-4COXMxUNe5sq9vtdJ9kwrRokJWMkgjpor6y8tX-fJ7pA650KV';
        $paypal_mode = 'sandbox'; // or 'live'
        
        // Get PayPal access token
        $ch = curl_init();
        $paypal_url = ($paypal_mode === 'sandbox') ? 
            'https://api.sandbox.paypal.com/v1/oauth2/token' : 
            'https://api.paypal.com/v1/oauth2/token';
            
        curl_setopt($ch, CURLOPT_URL, $paypal_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_USERPWD, $paypal_client_id . ':' . $paypal_secret);
        curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Accept-Language: en_US'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            error_log($log_prefix . "Failed to get PayPal access token, HTTP code: $httpCode");
            return false;
        }

        $tokenData = json_decode($response, true);
        $accessToken = $tokenData['access_token'];

        // Begin transaction
        $pdo->beginTransaction();

        // Create PayPal payment using vaulted payment token or setup token
        $vault_id = $paypalMethod['vault_id'] ?? $paypalMethod['payment_method_id'];
        $setup_token = $paypalMethod['setup_token'];
        
        if (!$vault_id && !$setup_token) {
            error_log($log_prefix . "No PayPal vault ID or setup token found for payment method");
            return false;
        }
        
        // If we only have setup_token, we need to complete the vaulting first
        if (!$vault_id && $setup_token) {
            error_log($log_prefix . "Found setup token but no vault ID, attempting to complete vaulting for PayPal payment method");
            error_log($log_prefix . "PayPal setup token found but vaulting incomplete. Setup token: $setup_token");
            return false;
        }
        

        
        // Create payment using vaulted payment token
        $payment_data = [
            'intent' => 'CAPTURE',
            'payment_source' => [
                'token' => [
                    'id' => $vault_id,
                    'type' => 'PAYMENT_METHOD_TOKEN'
                ]
            ],
            'purchase_units' => [
                [
                    'amount' => [
                        'currency_code' => 'EUR',
                        'value' => number_format($invoice_amount, 2, '.', '')
                    ],
                    'description' => "Automatic renewal payment for invoice #$invoice_id",
                    'invoice_id' => "invoice_{$invoice_id}_" . time() . "_" . rand(1000, 9999)
                ]
            ]
        ];

        // Create order using vaulted token
        $ch = curl_init();
        $paypal_url = ($paypal_mode === 'sandbox') ? 
            'https://api-m.sandbox.paypal.com/v2/checkout/orders' : 
            'https://api-m.paypal.com/v2/checkout/orders';
            
        curl_setopt($ch, CURLOPT_URL, $paypal_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payment_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $accessToken,
            'PayPal-Request-Id: ' . uniqid('order_', true)
        ]);

        $order_response = curl_exec($ch);
        $order_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $order_result = json_decode($order_response, true);
        
        if ($order_http_code !== 201 || !isset($order_result['id'])) {
            error_log($log_prefix . "PayPal order creation failed: " . ($order_result['message'] ?? 'HTTP ' . $order_http_code));
            return false;
        }

        $order_id = $order_result['id'];
        $order_status = $order_result['status'] ?? '';
        
        // Check if payment is already completed (common with vault tokens)
        if ($order_status === 'COMPLETED' && isset($order_result['purchase_units'][0]['payments']['captures'][0]['id'])) {
            // Payment was automatically captured
            $paypal_payment_id = $order_result['purchase_units'][0]['payments']['captures'][0]['id'];
            error_log($log_prefix . "PayPal payment automatically completed during order creation");
        } else {
            // Need to capture manually
            $ch = curl_init();
            $capture_url = ($paypal_mode === 'sandbox') ? 
                "https://api-m.sandbox.paypal.com/v2/checkout/orders/$order_id/capture" : 
                "https://api-m.paypal.com/v2/checkout/orders/$order_id/capture";
                
            curl_setopt($ch, CURLOPT_URL, $capture_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $accessToken,
                'PayPal-Request-Id: ' . uniqid('capture_', true)
            ]);

            $capture_response = curl_exec($ch);
            $capture_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $capture_result = json_decode($capture_response, true);
            
            if ($capture_http_code !== 201 || !isset($capture_result['purchase_units'][0]['payments']['captures'][0]['id'])) {
                error_log($log_prefix . "PayPal capture failed: " . ($capture_result['message'] ?? 'HTTP ' . $capture_http_code));
                return false;
            }

            $paypal_payment_id = $capture_result['purchase_units'][0]['payments']['captures'][0]['id'];
        }
        
        // Update invoice as paid
        $updateQuery = "UPDATE invoices
                        SET paid = 1,
                            date = NOW(),
                            status = 'Paid'
                        WHERE id = :invoice_id";
        $updateStmt = $pdo->prepare($updateQuery);
        $updateStmt->bindValue(":invoice_id", $invoice_id);
        $updateStmt->execute();

        // Create transaction record
        $txn_id = "PP" . time() . rand(1000, 9999);
        $txnSql = "INSERT INTO invoice_transactions
            (id, invoice_id, user_id, date, type, amount, description, status, processor, payment_intent_id)
            VALUES
            ('$txn_id', '$invoice_id', $user_id, NOW(), 'Auto PayPal Payment', $invoice_amount, 'Automatic renewal payment via PayPal', 'Completed', 'paypal', '$paypal_payment_id')";
        $pdo->exec($txnSql);

        // Commit the transaction
        $pdo->commit();
        
        error_log($log_prefix . "Successfully processed PayPal payment for invoice $invoice_id, Payment ID: $paypal_payment_id");
        return true;

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log($log_prefix . "Error processing PayPal payment: " . $e->getMessage());
        return false;
    }
}



/**
 * Function to attempt automatic credit payment for an invoice
 */
function attemptCreditPayment($pdo, $invoice_id, $user_id, $invoice_amount, $log_prefix) {
    try {
        error_log($log_prefix . "Attempting credit payment for invoice $invoice_id, user $user_id, amount $invoice_amount");

        // Begin transaction
        $pdo->beginTransaction();

        // Check available credit
        $creditQuery = "SELECT
            SUM(CASE WHEN (type = 'purchase' OR type = 'free') AND status = 'available' THEN amount ELSE 0 END) as regular_credit,
            SUM(CASE WHEN type = 'free' AND status = 'available' THEN amount ELSE 0 END) as free_credit
           FROM user_credits
           WHERE user_id = :user_id";
        $creditStmt = $pdo->prepare($creditQuery);
        $creditStmt->bindValue(":user_id", $user_id);
        $creditStmt->execute();
        $creditInfo = $creditStmt->fetch(PDO::FETCH_ASSOC);

        $total_available_credit = floatval($creditInfo["regular_credit"] ?? 0) + floatval($creditInfo["free_credit"] ?? 0);
        
        error_log($log_prefix . "Available credit: $total_available_credit, Required: $invoice_amount");

        if ($total_available_credit < $invoice_amount) {
            $pdo->rollBack();
            error_log($log_prefix . "Insufficient credit for full payment. Available: $total_available_credit, Required: $invoice_amount");
            return false;
        }

        // Apply credit to invoice
        $updateQuery = "UPDATE invoices
                        SET value = 0,
                            credit = COALESCE(credit, 0) + :credit_amount,
                            credit_applied = 1,
                            paid = 1,
                            date = NOW(),
                            status = 'Paid'
                        WHERE id = :invoice_id";
        $updateStmt = $pdo->prepare($updateQuery);
        $updateStmt->bindValue(":credit_amount", $invoice_amount);
        $updateStmt->bindValue(":invoice_id", $invoice_id);
        $updateStmt->execute();
        
        error_log($log_prefix . "Updated invoice $invoice_id with credit payment");

        // Deduct credit from user's account
        $creditUsageQuery = "INSERT INTO user_credits
                          (user_id, amount, type, status, description, created, invoice_id)
                          VALUES
                          (:user_id, :amount, 'used', 'applied', :description, NOW(), :invoice_id)";
        $creditUsageStmt = $pdo->prepare($creditUsageQuery);
        $creditUsageStmt->execute([
            ":user_id" => $user_id,
            ":amount" => -$invoice_amount, // Negative amount
            ":description" => "Auto-renewal payment for invoice #$invoice_id",
            ":invoice_id" => $invoice_id,
        ]);
        
        error_log($log_prefix . "Created negative credit entry for $invoice_amount");

        // Create transaction record
        $txn_id = "TXN" . time() . rand(1000, 9999);
        $txnSql = "INSERT INTO invoice_transactions
            (id, invoice_id, user_id, date, type, amount, description, status, processor)
            VALUES
            ('$txn_id', '$invoice_id', $user_id, NOW(), 'Auto Credit Payment', $invoice_amount, 'Automatic renewal payment via credit', 'Completed', 'System')";
        $pdo->exec($txnSql);
        
        error_log($log_prefix . "Created transaction record $txn_id");

        // Commit the transaction
        $pdo->commit();
        
        error_log($log_prefix . "Successfully processed credit payment for invoice $invoice_id");
        return true;

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log($log_prefix . "Error processing credit payment: " . $e->getMessage());
        return false;
    }
}

/**
 * Function to handle post-payment processing (renewal updates)
 */
function processInvoicePaymentCompletion($pdo, $invoice_id, $log_prefix) {
    try {
        error_log($log_prefix . "Processing payment completion for invoice $invoice_id");
        
        // Get invoice details
        $invoiceSql = "SELECT * FROM invoices WHERE id = :invoice_id";
        $invoiceStmt = $pdo->prepare($invoiceSql);
        $invoiceStmt->bindValue(":invoice_id", $invoice_id);
        $invoiceStmt->execute();
        $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

        if (!$invoice) {
            error_log($log_prefix . "Invoice not found: $invoice_id");
            return false;
        }

        // Get invoice items to determine the type and process accordingly
        $itemsQuery = "SELECT * FROM invoice_items WHERE invoice_id = :invoice_id";
        $itemsStmt = $pdo->prepare($itemsQuery);
        $itemsStmt->bindValue(":invoice_id", $invoice_id);
        $itemsStmt->execute();
        $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Process Server Renewal (most common case for auto-renewal)
        foreach ($items as $item) {
            if (strpos(strtolower($item['description']), 'service:') !== false && 
                strpos(strtolower($item['description']), 'period:') !== false) {
                
                // Extract period from description to calculate new due date
                $period = $item['period'] ?? 'Monthly';
                
                // Parse period string if it contains date range (e.g., "01/01/2023-01/02/2023")
                if (preg_match('/\d{2}\/\d{2}\/\d{4}-(\d{2}\/\d{2}\/\d{4})/', $period, $matches)) {
                    $end_date_str = $matches[1];
                    // Convert to MySQL date format
                    $date_parts = explode('/', $end_date_str);
                    $new_due_date = $date_parts[2] . '-' . $date_parts[1] . '-' . $date_parts[0];
                } else {
                    // Find the related orders_items to get current due date first
                    $findOrderItemQuery = "SELECT oi.id, oi.due_date 
                                         FROM orders_items oi
                                         JOIN orders o ON oi.order_id = o.id
                                         WHERE oi.user_id = :user_id 
                                         AND oi.status = 'active'
                                         AND DATE(oi.due_date) <= DATE(NOW() + INTERVAL 15 DAY)
                                         ORDER BY oi.due_date ASC
                                         LIMIT 1";
                    $findOrderItemStmt = $pdo->prepare($findOrderItemQuery);
                    $findOrderItemStmt->bindValue(":user_id", $invoice['user_id']);
                    $findOrderItemStmt->execute();
                    $orderItem = $findOrderItemStmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$orderItem) {
                        error_log($log_prefix . "Could not find orders_items to update for user {$invoice['user_id']}");
                        continue;
                    }
                    
                    // Calculate new due date based on current due date and payment period
                    $current_due_date = $orderItem['due_date'];
                    if (strtolower($period) == 'quarterly') {
                        $new_due_date = date('Y-m-d', strtotime('+3 months', strtotime($current_due_date)));
                    } elseif (strtolower($period) == 'semi-annual' || strtolower($period) == 'semiannual') {
                        $new_due_date = date('Y-m-d', strtotime('+6 months', strtotime($current_due_date)));
                    } elseif (strtolower($period) == 'annual' || strtolower($period) == 'yearly') {
                        $new_due_date = date('Y-m-d', strtotime('+1 year', strtotime($current_due_date)));
                    } else {
                        // Default to monthly
                        $new_due_date = date('Y-m-d', strtotime('+1 month', strtotime($current_due_date)));
                    }
                    
                    error_log($log_prefix . "Calculated new due date: $new_due_date (from current due date: $current_due_date, period: $period)");
                }
                
                // Update due_date in orders_items (we already have $orderItem from above)
                $updateSql = "UPDATE orders_items SET due_date = :due_date WHERE id = :id";
                $updateStmt = $pdo->prepare($updateSql);
                $updateStmt->bindValue(":due_date", $new_due_date);
                $updateStmt->bindValue(":id", $orderItem['id']);
                $updateStmt->execute();
                
                error_log($log_prefix . "Updated due_date for orders_items ID {$orderItem['id']} to $new_due_date");
                
                // Also update the order expiration date
                $orderSql = "UPDATE orders o 
                            JOIN orders_items oi ON o.id = oi.order_id 
                            SET o.expiration_date = :expiration_date 
                            WHERE oi.id = :orders_items_id";
                $orderStmt = $pdo->prepare($orderSql);
                $orderStmt->bindValue(":expiration_date", $new_due_date);
                $orderStmt->bindValue(":orders_items_id", $orderItem['id']);
                $orderStmt->execute();
                
                error_log($log_prefix . "Updated order expiration_date for orders_items ID {$orderItem['id']}");
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log($log_prefix . "Error in processInvoicePaymentCompletion: " . $e->getMessage());
        return false;
    }
}

try {
    // Calculate the date 14 days from now
    $due_date_target = date('Y-m-d', strtotime('+14 days'));
    error_log($log_prefix . "Looking for services due on: " . $due_date_target);
    echo "Looking for services due on: " . $due_date_target . "\n";

    // Debug: Check if there are any records in orders_items
    $countStmt = $pdo->query("SELECT COUNT(*) as count FROM orders_items");
    $countResult = $countStmt->fetch(PDO::FETCH_ASSOC);
    echo "Total records in orders_items: {$countResult['count']}\n";

    // Debug: Check if there are any records with the target due date
    $dueDateCheckStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders_items WHERE DATE(due_date) = :due_date");
    $dueDateCheckStmt->bindValue(':due_date', $due_date_target);
    $dueDateCheckStmt->execute();
    $dueDateResult = $dueDateCheckStmt->fetch(PDO::FETCH_ASSOC);
    echo "Records with due date {$due_date_target}: {$dueDateResult['count']}\n";

    // Find services with due dates 14 days from now - include auto_renewal and use_credit fields
    $query = "SELECT oi.*, o.status as order_status, u.id as user_id, u.email as user_email,
              u.first_name, u.last_name, u.company_name, u.country,
              dc.cpu as cpu_name, db.name as bandwidth_name, ds.name as storage_name,
              dsub.name as subnet_name, c.name as location_name, c.city as location_city,
              c.country as location_country, c.datacenter as location_datacenter,
              oi.auto_renewal, oi.use_credit
              FROM orders_items oi
              LEFT JOIN orders o ON oi.order_id = o.id
              LEFT JOIN users u ON oi.user_id = u.id
              LEFT JOIN dedicated_cpu dc ON oi.cpu_id = dc.id
              LEFT JOIN dedicated_bandwidth db ON oi.bandwidth_id = db.id
              LEFT JOIN dedicated_storages ds ON oi.storage_id = ds.id
              LEFT JOIN dedicated_subnets dsub ON oi.subnet_id = dsub.id
              LEFT JOIN dedicated_cities c ON oi.location_id = c.id
              WHERE DATE(oi.due_date) = :due_date_target
              AND (oi.status = 'active' OR o.status = 'active')";

    $stmt = $pdo->prepare($query);
    $stmt->bindValue(':due_date_target', $due_date_target);
    $stmt->execute();

    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stats['services_checked'] = count($services);

    error_log($log_prefix . "Found " . count($services) . " services due for renewal");
    echo "Found " . count($services) . " services due for renewal\n";

    // Only process services that are exactly 14 days away from due date
    if (count($services) == 0) {
        echo "No services found with due date exactly 14 days from now. No invoices will be generated.\n";
        error_log($log_prefix . "No services found with due date exactly 14 days from now. Exiting.");
    }

    // Process each service
    foreach ($services as $service) {
        error_log($log_prefix . "Processing service ID: " . $service['id'] . " for user: " . $service['user_email']);

        try {
            // Begin transaction
            $pdo->beginTransaction();

            // Check if auto_renewal is enabled (default to enabled if not set)
            $auto_renewal_enabled = isset($service['auto_renewal']) ? (bool)$service['auto_renewal'] : true;
            
            // Check if use_credit is 'yes' (default to 'yes' if not set)
            $use_credit_enabled = isset($service['use_credit']) ? 
                (strtolower(trim($service['use_credit'])) === 'yes' || $service['use_credit'] === '1' || $service['use_credit'] === 1) : 
                true;
            
            error_log($log_prefix . "Service ID {$service['id']}: auto_renewal=" . ($auto_renewal_enabled ? 'enabled' : 'disabled') . 
                     ", use_credit=" . ($use_credit_enabled ? 'yes' : 'no'));

            // Generate invoice number
            $invoiceNumberStmt = $pdo->query("SELECT MAX(CAST(invoice_number AS UNSIGNED)) AS last_number FROM invoices");
            $invoiceNumberResult = $invoiceNumberStmt->fetch(PDO::FETCH_ASSOC);
            $nextInvoiceNumber = ($invoiceNumberResult && $invoiceNumberResult['last_number'])
                ? $invoiceNumberResult['last_number'] + 1
                : 1;

            // Generate proforma number
            // Get the highest proforma number
            $proformaNumberStmt = $pdo->query("SELECT MAX(CAST(proforma_number AS UNSIGNED)) AS last_number FROM invoices WHERE proforma_number REGEXP '^[0-9]+$'");
            $proformaNumberResult = $proformaNumberStmt->fetch(PDO::FETCH_ASSOC);
            $lastNumericProforma = ($proformaNumberResult && $proformaNumberResult['last_number'])
                ? $proformaNumberResult['last_number']
                : 0;

            // Get the next available number
            $nextProformaNumber = $lastNumericProforma + 1;
            $proformaNumber = $nextProformaNumber;

            error_log($log_prefix . "Generated proforma number: {$proformaNumber} (Last numeric: {$lastNumericProforma})");

            // Get user's country for VAT calculation
            $country = $service['country'];

            // Get VAT rate based on country
            $vatStmt = $pdo->prepare("SELECT rate FROM vat_rates WHERE country = :country");
            $vatStmt->bindValue(':country', $country);
            $vatStmt->execute();
            $vatResult = $vatStmt->fetch(PDO::FETCH_ASSOC);
            $tax_rate = ($vatResult && $vatResult['rate']) ? $vatResult['rate'] / 100 : 0;

            // Calculate amounts
            $subtotal = $service['order_price'] ?? 0;
            $tax = round($subtotal * $tax_rate, 2);
            $total = $subtotal + $tax;

            // Create description
            $period = ucfirst(strtolower($service['payment_period'] ?? "Monthly"));
            $nextDueDate = date('Y-m-d', strtotime('+1 month', strtotime($due_date_target)));

            // Adjust next due date based on payment period
            if (strtolower($period) == 'quarterly') {
                $nextDueDate = date('Y-m-d', strtotime('+3 months', strtotime($due_date_target)));
            } else if (strtolower($period) == 'semi-annual' || strtolower($period) == 'semiannual') {
                $nextDueDate = date('Y-m-d', strtotime('+6 months', strtotime($due_date_target)));
            } else if (strtolower($period) == 'annual' || strtolower($period) == 'yearly') {
                $nextDueDate = date('Y-m-d', strtotime('+1 year', strtotime($due_date_target)));
            }

            // Build server details string
            $serverDetails = [];
            if (!empty($service['cpu_name'])) $serverDetails[] = "CPU: {$service['cpu_name']}";
            if (!empty($service['storage_name'])) $serverDetails[] = "Storage: {$service['storage_name']}";
            if (!empty($service['bandwidth_name'])) $serverDetails[] = "Bandwidth: {$service['bandwidth_name']}";
            if (!empty($service['subnet_name'])) $serverDetails[] = "Subnet: {$service['subnet_name']}";

            // Location details
            $location = '';
            if (!empty($service['location_name'])) {
                $location = $service['location_name'];
                if (!empty($service['location_city']) || !empty($service['location_country']) || !empty($service['location_datacenter'])) {
                    $locationParts = [];
                    if (!empty($service['location_city'])) $locationParts[] = $service['location_city'];
                    if (!empty($service['location_country'])) $locationParts[] = $service['location_country'];
                    if (!empty($service['location_datacenter'])) $locationParts[] = $service['location_datacenter'];
                    $location .= " (" . implode(", ", $locationParts) . ")";
                }
                $serverDetails[] = "Location: {$location}";
            }

            // Create a description that's compatible with the invoice system
            // Use a single line with separators instead of newlines
            $descriptionParts = [];
            $descriptionParts[] = "Service: " . ($service['hostname'] ?? "Server");
            $descriptionParts[] = "Period: " . $period;
            $descriptionParts[] = "Dates: " . date('Y-m-d', strtotime($due_date_target)) . " to " . $nextDueDate;

            // Add server details
            if (!empty($service['cpu_name'])) $descriptionParts[] = "CPU: {$service['cpu_name']}";
            if (!empty($service['storage_name'])) $descriptionParts[] = "Storage: {$service['storage_name']}";
            if (!empty($service['bandwidth_name'])) $descriptionParts[] = "Bandwidth: {$service['bandwidth_name']}";
            if (!empty($service['subnet_name'])) $descriptionParts[] = "Subnet: {$service['subnet_name']}";

            // Add location
            if (!empty($location)) {
                $descriptionParts[] = "Location: {$location}";
            }

            // Join with a separator that the invoice system can handle
            $description = implode(" | ", $descriptionParts);

            // Create the invoice description with the automatically generated text
            $invoiceDescription = "Automatically generated invoice on: " . date('Y-m-d H:i:s');

            // Determine invoice type and status based on auto_renewal
            $invoiceType = $auto_renewal_enabled ? 'invoice' : 'proforma';
            $invoiceStatus = $auto_renewal_enabled ? 'Pending' : 'Pending';

            // Insert the invoice
            $invoiceStmt = $pdo->prepare("
                INSERT INTO invoices (
                    user_id,
                    order_id,
                    type,
                    value,
                    subtotal,
                    tax,
                    date,
                    due_date,
                    payment_method,
                    status,
                    proforma_number,
                    invoice_number,
                    description,
                    paid
                ) VALUES (
                    :user_id,
                    :order_id,
                    :type,
                    :value,
                    :subtotal,
                    :tax,
                    NOW(),
                    :due_date,
                    'Auto Payment',
                    :status,
                    :proforma_number,
                    :invoice_number,
                    :description,
                    0
                )
            ");

            $invoiceStmt->bindValue(':user_id', $service['user_id']);
            $invoiceStmt->bindValue(':order_id', $service['order_id']);
            $invoiceStmt->bindValue(':type', $invoiceType);
            $invoiceStmt->bindValue(':value', $total);
            $invoiceStmt->bindValue(':subtotal', $subtotal);
            $invoiceStmt->bindValue(':tax', $tax);
            $invoiceStmt->bindValue(':due_date', $due_date_target);
            $invoiceStmt->bindValue(':proforma_number', $proformaNumber);
            $invoiceStmt->bindValue(':invoice_number', $nextInvoiceNumber);
            $invoiceStmt->bindValue(':description', $invoiceDescription);
            $invoiceStmt->bindValue(':status', $invoiceStatus);

            $invoiceStmt->execute();
            $invoiceId = $pdo->lastInsertId();

            // Create invoice item
            $itemStmt = $pdo->prepare("
                INSERT INTO invoice_items (
                    invoice_id,
                    description,
                    quantity,
                    unit_price,
                    total,
                    period
                ) VALUES (
                    :invoice_id,
                    :description,
                    :quantity,
                    :unit_price,
                    :total,
                    :period
                )
            ");

            $itemStmt->bindValue(':invoice_id', $invoiceId);
            $itemStmt->bindValue(':description', $description);
            $itemStmt->bindValue(':quantity', 1);
            $itemStmt->bindValue(':unit_price', $subtotal);
            $itemStmt->bindValue(':total', $subtotal);
            $itemStmt->bindValue(':period', $service['payment_period'] ?? 'Monthly');

            $itemStmt->execute();

            // Log to activity log
            $activityStmt = $pdo->prepare("
                INSERT INTO activity_log (
                    user_id,
                    action,
                    description,
                    user_name,
                    timestamp,
                    activity_type,
                    invoice_number,
                    proforma_number
                ) VALUES (
                    :user_id,
                    'Invoice Generated',
                    :description,
                    :user_name,
                    NOW(),
                    'billing',
                    :invoice_number,
                    :proforma_number
                )
            ");

            $userName = trim($service['first_name'] . ' ' . $service['last_name']);
            if (empty($userName)) {
                $userName = $service['company_name'] ?? $service['user_email'];
            }

            $activityStmt->bindValue(':user_id', $service['user_id']);
            $activityStmt->bindValue(':description', "Automatic invoice generation for service due in 14 days");
            $activityStmt->bindValue(':user_name', $userName);
            $activityStmt->bindValue(':invoice_number', $nextInvoiceNumber);
            $activityStmt->bindValue(':proforma_number', $proformaNumber);

            $activityStmt->execute();

            // Commit invoice creation transaction
            $pdo->commit();

            $stats['invoices_generated']++;
            error_log($log_prefix . "Successfully generated invoice #$nextInvoiceNumber (Proforma: $proformaNumber) for service ID: " . $service['id']);

            // Now attempt automatic payment if auto_renewal is enabled
            if ($auto_renewal_enabled) {
                $stats['automatic_payments_attempted']++;
                error_log($log_prefix . "Attempting automatic payment for invoice #$nextInvoiceNumber (auto_renewal enabled)");

                $payment_successful = false;

                // Try payment methods in order of priority
                // 1. Credit payment first if use_credit is enabled
                if ($use_credit_enabled) {
                    error_log($log_prefix . "Attempting credit payment for invoice #$nextInvoiceNumber");
                    $payment_successful = attemptCreditPayment($pdo, $invoiceId, $service['user_id'], $total, $log_prefix);
                    
                    if ($payment_successful) {
                        $stats['automatic_payments_successful']++;
                        $stats['credit_payments_successful']++;
                        error_log($log_prefix . "Successfully processed automatic credit payment for invoice #$nextInvoiceNumber");
                        $payment_method_used = 'credit';
                    } else {
                        error_log($log_prefix . "Credit payment failed for invoice #$nextInvoiceNumber (insufficient credit or error)");
                    }
                }

                // 2. Try Stripe payment if credit failed or not enabled
                if (!$payment_successful) {
                    error_log($log_prefix . "Attempting Stripe payment for invoice #$nextInvoiceNumber");
                    if (class_exists('\Stripe\Stripe')) {
                        $payment_successful = attemptStripePayment($pdo, $invoiceId, $service['user_id'], $total, $log_prefix);
                        if ($payment_successful) {
                            $stats['automatic_payments_successful']++;
                            $stats['stripe_payments_successful']++;
                            error_log($log_prefix . "Successfully processed automatic Stripe payment for invoice #$nextInvoiceNumber");
                            $payment_method_used = 'stripe';
                        } else {
                            error_log($log_prefix . "Stripe payment failed for invoice #$nextInvoiceNumber");
                        }
                    } else {
                        error_log($log_prefix . "Stripe library not available, skipping Stripe payment");
                    }
                }

                // 3. Try PayPal payment if both credit and Stripe failed
                if (!$payment_successful) {
                    error_log($log_prefix . "Attempting PayPal payment for invoice #$nextInvoiceNumber");
                    $payment_successful = attemptPayPalPayment($pdo, $invoiceId, $service['user_id'], $total, $log_prefix);
                    if ($payment_successful) {
                        $stats['automatic_payments_successful']++;
                        $stats['paypal_payments_successful']++;
                        error_log($log_prefix . "Successfully processed automatic PayPal payment for invoice #$nextInvoiceNumber");
                        $payment_method_used = 'paypal';
                    } else {
                        error_log($log_prefix . "PayPal payment failed for invoice #$nextInvoiceNumber");
                    }
                }

          

                // Process payment completion if any payment method succeeded
                if ($payment_successful) {
                    // Process payment completion (update due dates, etc.)
                    processInvoicePaymentCompletion($pdo, $invoiceId, $log_prefix);

                    // Log successful auto-renewal
                    $autoRenewalActivityStmt = $pdo->prepare("
                        INSERT INTO activity_log (
                            user_id,
                            action,
                            description,
                            user_name,
                            timestamp,
                            activity_type,
                            invoice_number,
                            proforma_number
                        ) VALUES (
                            :user_id,
                            'Auto Renewal Payment',
                            :description,
                            :user_name,
                            NOW(),
                            'billing',
                            :invoice_number,
                            :proforma_number
                        )
                    ");

                    $autoRenewalActivityStmt->bindValue(':user_id', $service['user_id']);
                    $autoRenewalActivityStmt->bindValue(':description', "Automatic renewal payment processed via {$payment_method_used} for service");
                    $autoRenewalActivityStmt->bindValue(':user_name', $userName);
                    $autoRenewalActivityStmt->bindValue(':invoice_number', $nextInvoiceNumber);
                    $autoRenewalActivityStmt->bindValue(':proforma_number', $proformaNumber);
                    $autoRenewalActivityStmt->execute();
                }
                
                if (!$payment_successful) {
                    error_log($log_prefix . "Automatic payment failed for invoice #$nextInvoiceNumber - invoice remains unpaid");
                }
            } else {
                error_log($log_prefix . "Auto-renewal disabled for service ID {$service['id']} - invoice generated but no automatic payment attempted");
            }

            // Send email notification with additional error handling
            try {
                error_log($log_prefix . "Attempting to send email notification for invoice #$nextInvoiceNumber to " . $service['user_email']);

                // Try to send email notification if function is available
                $emailSent = false;
                if (file_exists("./email_functions.php") && function_exists('sendInvoiceGeneratedEmail')) {
                    try {
                        // Call the function dynamically to avoid linter warnings
                        $emailFunction = 'sendInvoiceGeneratedEmail';
                        $emailResult = call_user_func($emailFunction, $invoiceId);
                        if ($emailResult) {
                            error_log($log_prefix . "Successfully sent email notification for invoice #$nextInvoiceNumber to " . $service['user_email']);
                            echo "Successfully sent email notification for invoice #$nextInvoiceNumber to " . $service['user_email'] . "\n";
                            $emailSent = true;
                        } else {
                            error_log($log_prefix . "Email function returned false for invoice #$nextInvoiceNumber");
                        }
                    } catch (Exception $e) {
                        error_log($log_prefix . "Error calling sendInvoiceGeneratedEmail: " . $e->getMessage());
                    }
                }
                
                if (!$emailSent) {
                    error_log($log_prefix . "Email notification not sent for invoice #$nextInvoiceNumber - function not available or failed");
                    echo "Email notification not sent for invoice #$nextInvoiceNumber\n";
                }
            } catch (Exception $emailError) {
                error_log($log_prefix . "Error in email notification process: " . $emailError->getMessage());
                // Log the stack trace for better debugging
                error_log($log_prefix . "Stack trace: " . $emailError->getTraceAsString());
            }

        } catch (Exception $e) {
            // Rollback transaction on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }

            $stats['errors']++;
            error_log($log_prefix . "Error generating invoice for service ID " . $service['id'] . ": " . $e->getMessage());
            echo "Error generating invoice for service ID " . $service['id'] . ": " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    // Log completion
    $execution_time = round(microtime(true) - $start_time, 2);
    error_log($log_prefix . "Invoice generation completed in $execution_time seconds. " .
              "Services checked: " . $stats['services_checked'] . ", " .
              "Invoices generated: " . $stats['invoices_generated'] . ", " .
              "Automatic payments attempted: " . $stats['automatic_payments_attempted'] . ", " .
              "Automatic payments successful: " . $stats['automatic_payments_successful'] . ", " .
              "Credit payments successful: " . $stats['credit_payments_successful'] . ", " .
              "Stripe payments successful: " . $stats['stripe_payments_successful'] . ", " .
              "PayPal payments successful: " . $stats['paypal_payments_successful'] . ", " .
 
              "Errors: " . $stats['errors']);

    echo "\n=== INVOICE GENERATION SUMMARY ===\n";
    echo "Services checked: " . $stats['services_checked'] . "\n";
    echo "Invoices generated: " . $stats['invoices_generated'] . "\n";
    echo "Automatic payments attempted: " . $stats['automatic_payments_attempted'] . "\n";
    echo "Automatic payments successful: " . $stats['automatic_payments_successful'] . "\n";
    echo "Credit payments successful: " . $stats['credit_payments_successful'] . "\n";
    echo "Stripe payments successful: " . $stats['stripe_payments_successful'] . "\n";
    echo "PayPal payments successful: " . $stats['paypal_payments_successful'] . "\n";

    echo "Errors: " . $stats['errors'] . "\n";
    echo "Execution time: $execution_time seconds\n";

} catch (Exception $e) {
    error_log($log_prefix . "Fatal error in invoice generation script: " . $e->getMessage());
    echo "Fatal error in invoice generation script: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}

exit(0);
