<?php
// Debug 240GB SSD compatibility logic with location checking
require_once 'mysql.php';

echo "=== Debugging 240GB SSD Compatibility with Location ===\n\n";

try {
    $cpu_id = 1; // Dual Intel Xeon E5-2630v3
    $storage_id = 1; // 240GB SSD
    $bandwidth_id = 1; // 1 Gbps
    $country_id = 1; // Romania
    $city_id = 1; // Bucharest
    
    echo "Test parameters:\n";
    echo "- CPU ID: $cpu_id\n";
    echo "- Storage ID: $storage_id (240GB SSD)\n";
    echo "- Bandwidth ID: $bandwidth_id\n";
    echo "- Country ID: $country_id\n";
    echo "- City ID: $city_id\n\n";
    
    // Test dedicated servers with location filtering
    echo "=== Testing Dedicated Servers with Location ===\n";
    
    // Check servers with 240GB SSD (storage_id = 1)
    echo "--- Servers with 240GB SSD (storage_id = 1) ---\n";
    $storage_1_query = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT id,
            (CASE WHEN bay1 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bay2 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bay3 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bay4 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bay5 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bay6 = 1 THEN 1 ELSE 0 END) as matching_bays,
            (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
            FROM inventory_dedicated_servers
            WHERE status = 'Available'
            AND country_id = ?
            AND order_id IS NULL
            AND cpu = ?
        ) subquery
        WHERE matching_bays >= 1 AND total_port_speed >= 1000
    ");
    $storage_1_query->execute([$country_id, $cpu_id]);
    $storage_1_count = $storage_1_query->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Dedicated servers with 240GB SSD in country $country_id: $storage_1_count\n";
    
    // Check servers with 2x500GB SSD (storage_id = 2)
    echo "\n--- Servers with 2x500GB SSD (storage_id = 2) ---\n";
    $storage_2_query = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT id,
            (CASE WHEN bay1 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bay2 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bay3 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bay4 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bay5 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bay6 = 2 THEN 1 ELSE 0 END) as matching_bays,
            (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
            FROM inventory_dedicated_servers
            WHERE status = 'Available'
            AND country_id = ?
            AND order_id IS NULL
            AND cpu = ?
        ) subquery
        WHERE matching_bays >= 2 AND total_port_speed >= 1000
    ");
    $storage_2_query->execute([$country_id, $cpu_id]);
    $storage_2_count = $storage_2_query->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Dedicated servers with 2x500GB SSD in country $country_id: $storage_2_count\n";
    
    $total_dedicated_compatible = $storage_1_count + $storage_2_count;
    echo "Total compatible dedicated servers: $total_dedicated_compatible\n";
    
    // Test blade servers with location filtering
    echo "\n=== Testing Blade Servers with Location ===\n";
    
    // Check blade servers with 240GB SSD (storage_id = 1)
    echo "--- Blade servers with 240GB SSD (storage_id = 1) ---\n";
    $blade_storage_1_query = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT bsi.id,
            (CASE WHEN bsi.bay1 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay2 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay3 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay4 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay5 = 1 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay6 = 1 THEN 1 ELSE 0 END) as matching_bays,
            (COALESCE(bsi.port1_speed, 0) + COALESCE(bsi.port2_speed, 0)) as total_port_speed
            FROM blade_server_inventory bsi
            LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
            WHERE bsi.status = 'Available'
            AND ich.city_id = ?
            AND bsi.order_id IS NULL
            AND bsi.cpu = ?
        ) subquery
        WHERE matching_bays >= 1 AND total_port_speed >= 1000
    ");
    $blade_storage_1_query->execute([$city_id, $cpu_id]);
    $blade_storage_1_count = $blade_storage_1_query->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Blade servers with 240GB SSD in city $city_id: $blade_storage_1_count\n";
    
    // Check blade servers with 2x500GB SSD (storage_id = 2)
    echo "\n--- Blade servers with 2x500GB SSD (storage_id = 2) ---\n";
    $blade_storage_2_query = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT bsi.id,
            (CASE WHEN bsi.bay1 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay2 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay3 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay4 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay5 = 2 THEN 1 ELSE 0 END) +
            (CASE WHEN bsi.bay6 = 2 THEN 1 ELSE 0 END) as matching_bays,
            (COALESCE(bsi.port1_speed, 0) + COALESCE(bsi.port2_speed, 0)) as total_port_speed
            FROM blade_server_inventory bsi
            LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
            WHERE bsi.status = 'Available'
            AND ich.city_id = ?
            AND bsi.order_id IS NULL
            AND bsi.cpu = ?
        ) subquery
        WHERE matching_bays >= 2 AND total_port_speed >= 1000
    ");
    $blade_storage_2_query->execute([$city_id, $cpu_id]);
    $blade_storage_2_count = $blade_storage_2_query->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Blade servers with 2x500GB SSD in city $city_id: $blade_storage_2_count\n";
    
    $total_blade_compatible = $blade_storage_1_count + $blade_storage_2_count;
    echo "Total compatible blade servers: $total_blade_compatible\n";
    
    $total_compatible = $total_dedicated_compatible + $total_blade_compatible;
    echo "\n=== SUMMARY ===\n";
    echo "Total compatible servers for 240GB SSD order: $total_compatible\n";
    echo "- Dedicated servers: $total_dedicated_compatible\n";
    echo "- Blade servers: $total_blade_compatible\n\n";
    
    // Test location filtering by checking servers in different locations
    echo "=== Testing Location Filtering ===\n";
    
    // Check servers in Frankfurt (country_id = 2, city_id = 2)
    echo "--- Checking Frankfurt (country_id = 2, city_id = 2) ---\n";
    $frankfurt_dedicated = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM inventory_dedicated_servers
        WHERE status = 'Available'
        AND country_id = 2
        AND order_id IS NULL
        AND cpu = ?
    ");
    $frankfurt_dedicated->execute([$cpu_id]);
    $frankfurt_dedicated_count = $frankfurt_dedicated->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Frankfurt dedicated servers with CPU $cpu_id: $frankfurt_dedicated_count\n";
    
    $frankfurt_blade = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM blade_server_inventory bsi
        LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
        WHERE bsi.status = 'Available'
        AND ich.city_id = 2
        AND bsi.order_id IS NULL
        AND bsi.cpu = ?
    ");
    $frankfurt_blade->execute([$cpu_id]);
    $frankfurt_blade_count = $frankfurt_blade->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Frankfurt blade servers with CPU $cpu_id: $frankfurt_blade_count\n";
    
    echo "\nThis confirms that location filtering is working correctly.\n";
    echo "The API should only show servers from the selected location.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
